/**
 * Metrics Overview Component for NeuroFlowAI Dashboard
 * ===================================================
 * 
 * High-level metrics overview with trend indicators and quick insights.
 */

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  LinearProgress,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  CloudQueue as CloudIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';

interface MetricsOverviewProps {
  metrics: {
    workflows: {
      active: number;
      completed_today: number;
      success_rate: number;
      avg_execution_time: number;
    };
    models: {
      deployed: number;
      total_requests_today: number;
      avg_latency: number;
      error_rate: number;
    };
    resources: {
      gpu_utilization: number;
      cpu_utilization: number;
      memory_usage: number;
      active_instances: number;
    };
    revenue: {
      daily_revenue: number;
      monthly_recurring: number;
      api_calls_today: number;
      active_subscriptions: number;
    };
  } | null;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: 'up' | 'down' | 'flat';
  trendValue?: string;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  icon?: React.ReactNode;
  progress?: number;
  maxProgress?: number;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  trend,
  trendValue,
  color = 'primary',
  icon,
  progress,
  maxProgress = 100,
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main' }} />;
      case 'down':
        return <TrendingDownIcon sx={{ fontSize: 16, color: 'error.main' }} />;
      case 'flat':
        return <TrendingFlatIcon sx={{ fontSize: 16, color: 'text.secondary' }} />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'success.main';
      case 'down':
        return 'error.main';
      case 'flat':
        return 'text.secondary';
      default:
        return 'text.secondary';
    }
  };

  return (
    <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
            {title}
          </Typography>
          {icon && (
            <Box sx={{ color: `${color}.main`, opacity: 0.7 }}>
              {icon}
            </Box>
          )}
        </Box>

        <Typography variant="h4" sx={{ fontWeight: 'bold', color: `${color}.main`, mb: 1 }}>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </Typography>

        {subtitle && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {subtitle}
          </Typography>
        )}

        {(trend || trendValue) && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            {getTrendIcon()}
            {trendValue && (
              <Typography variant="body2" sx={{ color: getTrendColor(), fontWeight: 500 }}>
                {trendValue}
              </Typography>
            )}
          </Box>
        )}

        {progress !== undefined && (
          <Box sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
              <Typography variant="caption" color="text.secondary">
                Utilization
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {progress.toFixed(1)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={(progress / maxProgress) * 100}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 3,
                  backgroundColor: progress > 80 ? 'error.main' : progress > 60 ? 'warning.main' : 'success.main',
                },
              }}
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export const MetricsOverview: React.FC<MetricsOverviewProps> = ({ metrics }) => {
  if (!metrics) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6">Loading metrics...</Typography>
        </CardContent>
      </Card>
    );
  }

  const formatLatency = (latency: number) => {
    if (latency < 1000) {
      return `${latency.toFixed(0)}ms`;
    }
    return `${(latency / 1000).toFixed(1)}s`;
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    }
    if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(1)}K`;
    }
    return `$${amount.toFixed(0)}`;
  };

  const getSuccessRateTrend = (rate: number) => {
    if (rate >= 0.95) return 'up';
    if (rate >= 0.85) return 'flat';
    return 'down';
  };

  const getLatencyTrend = (latency: number) => {
    if (latency <= 100) return 'up';
    if (latency <= 200) return 'flat';
    return 'down';
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
          System Overview
        </Typography>
        
        <Grid container spacing={3}>
          {/* Workflows */}
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Active Workflows"
              value={metrics.workflows.active}
              subtitle={`${metrics.workflows.completed_today} completed today`}
              trend="up"
              trendValue="+12%"
              color="primary"
              icon={<CloudIcon />}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Success Rate"
              value={`${(metrics.workflows.success_rate * 100).toFixed(1)}%`}
              subtitle="Workflow completion rate"
              trend={getSuccessRateTrend(metrics.workflows.success_rate)}
              trendValue={metrics.workflows.success_rate >= 0.95 ? "+2.1%" : "-1.3%"}
              color={metrics.workflows.success_rate >= 0.9 ? "success" : "warning"}
              progress={metrics.workflows.success_rate * 100}
            />
          </Grid>

          {/* Models */}
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Deployed Models"
              value={metrics.models.deployed}
              subtitle={`${metrics.models.total_requests_today.toLocaleString()} requests today`}
              trend="up"
              trendValue="+5 new"
              color="info"
              icon={<SpeedIcon />}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Avg Latency"
              value={formatLatency(metrics.models.avg_latency)}
              subtitle="Model response time"
              trend={getLatencyTrend(metrics.models.avg_latency)}
              trendValue={metrics.models.avg_latency <= 100 ? "-15ms" : "+23ms"}
              color={metrics.models.avg_latency <= 100 ? "success" : metrics.models.avg_latency <= 200 ? "warning" : "error"}
            />
          </Grid>

          {/* Resources */}
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="GPU Utilization"
              value={`${metrics.resources.gpu_utilization.toFixed(1)}%`}
              subtitle={`${metrics.resources.active_instances} active instances`}
              color={metrics.resources.gpu_utilization > 80 ? "warning" : "success"}
              icon={<MemoryIcon />}
              progress={metrics.resources.gpu_utilization}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="CPU Usage"
              value={`${metrics.resources.cpu_utilization.toFixed(1)}%`}
              subtitle="System CPU utilization"
              color={metrics.resources.cpu_utilization > 80 ? "error" : "primary"}
              progress={metrics.resources.cpu_utilization}
            />
          </Grid>

          {/* Revenue */}
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Daily Revenue"
              value={formatCurrency(metrics.revenue.daily_revenue)}
              subtitle="Today's earnings"
              trend="up"
              trendValue="+18%"
              color="success"
              icon={<MoneyIcon />}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="MRR"
              value={formatCurrency(metrics.revenue.monthly_recurring)}
              subtitle={`${metrics.revenue.active_subscriptions} subscriptions`}
              trend="up"
              trendValue="+$2.4K"
              color="success"
            />
          </Grid>
        </Grid>

        {/* Quick Insights */}
        <Box sx={{ mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Quick Insights
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {metrics.workflows.success_rate >= 0.95 && (
              <Chip
                label="Excellent workflow reliability"
                color="success"
                size="small"
                variant="outlined"
              />
            )}
            {metrics.models.avg_latency <= 100 && (
              <Chip
                label="Low latency performance"
                color="success"
                size="small"
                variant="outlined"
              />
            )}
            {metrics.resources.gpu_utilization > 85 && (
              <Chip
                label="High GPU utilization"
                color="warning"
                size="small"
                variant="outlined"
              />
            )}
            {metrics.models.error_rate < 0.01 && (
              <Chip
                label="Minimal error rate"
                color="success"
                size="small"
                variant="outlined"
              />
            )}
            {metrics.revenue.daily_revenue > 1000 && (
              <Chip
                label="Strong daily revenue"
                color="success"
                size="small"
                variant="outlined"
              />
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

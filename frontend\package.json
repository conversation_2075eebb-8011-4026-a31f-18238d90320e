{"name": "aiml-platform-frontend", "version": "2.0.0", "description": "Enterprise AI/ML Platform - React Frontend", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.3", "@mui/x-data-grid": "^6.10.1", "@mui/x-date-pickers": "^6.10.1", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.39", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "axios": "^1.4.0", "chart.js": "^4.3.3", "chartjs-adapter-date-fns": "^3.0.0", "d3": "^7.8.5", "date-fns": "^2.30.0", "formik": "^2.4.3", "framer-motion": "^10.16.1", "lodash": "^4.17.21", "plotly.js": "^2.25.2", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.45.2", "react-plotly.js": "^2.6.0", "react-query": "^3.39.3", "react-redux": "^8.1.2", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "react-virtualized": "^9.22.5", "recharts": "^2.7.2", "socket.io-client": "^4.7.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.2.0"}, "devDependencies": {"@types/d3": "^7.4.0", "@types/lodash": "^4.14.195", "@types/plotly.js": "^2.12.18", "@types/react-plotly.js": "^2.6.0", "@types/react-virtualized": "^9.21.21", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.0", "sass": "^1.64.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,scss}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "react-hooks/exhaustive-deps": "warn"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}
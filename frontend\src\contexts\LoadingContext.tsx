/**
 * Loading Context for the Enterprise AI/ML Platform.
 * 
 * Provides global loading state management for the application,
 * allowing components to show/hide loading indicators.
 */

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Backdrop, CircularProgress, Typography, Box } from '@mui/material';

// Types
interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

interface LoadingContextType {
  isLoading: boolean;
  message?: string;
  progress?: number;
  showLoading: (message?: string) => void;
  hideLoading: () => void;
  setProgress: (progress: number) => void;
  updateMessage: (message: string) => void;
}

// Create context
const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

// Custom hook to use loading context
export const useLoading = (): LoadingContextType => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

// Loading provider component
interface LoadingProviderProps {
  children: ReactNode;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    message: undefined,
    progress: undefined,
  });

  const showLoading = (message?: string): void => {
    setLoadingState({
      isLoading: true,
      message,
      progress: undefined,
    });
  };

  const hideLoading = (): void => {
    setLoadingState({
      isLoading: false,
      message: undefined,
      progress: undefined,
    });
  };

  const setProgress = (progress: number): void => {
    setLoadingState(prev => ({
      ...prev,
      progress: Math.max(0, Math.min(100, progress)),
    }));
  };

  const updateMessage = (message: string): void => {
    setLoadingState(prev => ({
      ...prev,
      message,
    }));
  };

  const contextValue: LoadingContextType = {
    isLoading: loadingState.isLoading,
    message: loadingState.message,
    progress: loadingState.progress,
    showLoading,
    hideLoading,
    setProgress,
    updateMessage,
  };

  return (
    <LoadingContext.Provider value={contextValue}>
      {children}
      
      {/* Global loading overlay */}
      <Backdrop
        sx={{
          color: '#fff',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          flexDirection: 'column',
          gap: 2,
        }}
        open={loadingState.isLoading}
      >
        <CircularProgress 
          color="inherit" 
          size={60}
          variant={loadingState.progress !== undefined ? 'determinate' : 'indeterminate'}
          value={loadingState.progress}
        />
        
        {loadingState.message && (
          <Typography variant="h6" component="div" textAlign="center">
            {loadingState.message}
          </Typography>
        )}
        
        {loadingState.progress !== undefined && (
          <Box sx={{ minWidth: 35 }}>
            <Typography variant="body2" color="inherit">
              {`${Math.round(loadingState.progress)}%`}
            </Typography>
          </Box>
        )}
      </Backdrop>
    </LoadingContext.Provider>
  );
};

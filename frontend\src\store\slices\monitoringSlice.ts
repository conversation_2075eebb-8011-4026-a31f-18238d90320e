/**
 * Monitoring slice for Redux store.
 * 
 * Manages system monitoring data including metrics, health status,
 * and performance data.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface SystemMetrics {
  cpu_percent: number;
  memory_percent: number;
  disk_percent: number;
  timestamp: string;
}

export interface HealthCheck {
  status: 'healthy' | 'warning' | 'error';
  timestamp: string;
  checks: {
    system: { status: string; cpu_percent: number };
    database: { status: string; connections: string };
    ml_service: { status: string; models_loaded: number };
    api: { status: string; uptime_seconds: number };
  };
}

export interface MonitoringState {
  systemMetrics: SystemMetrics | null;
  healthStatus: HealthCheck | null;
  isLoading: boolean;
  error: string | null;
  metricsHistory: SystemMetrics[];
}

const initialState: MonitoringState = {
  systemMetrics: null,
  healthStatus: null,
  isLoading: false,
  error: null,
  metricsHistory: [],
};

// Async thunks
export const fetchHealthStatus = createAsyncThunk(
  'monitoring/fetchHealthStatus',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/monitoring/health');
      if (!response.ok) throw new Error('Failed to fetch health status');
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

export const fetchMetrics = createAsyncThunk(
  'monitoring/fetchMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/monitoring/metrics');
      if (!response.ok) throw new Error('Failed to fetch metrics');
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

const monitoringSlice = createSlice({
  name: 'monitoring',
  initialState,
  reducers: {
    addMetricsToHistory: (state, action: PayloadAction<SystemMetrics>) => {
      state.metricsHistory.push(action.payload);
      // Keep only last 100 entries
      if (state.metricsHistory.length > 100) {
        state.metricsHistory = state.metricsHistory.slice(-100);
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchHealthStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchHealthStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.healthStatus = action.payload;
      })
      .addCase(fetchHealthStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchMetrics.fulfilled, (state, action) => {
        state.systemMetrics = action.payload.system;
        // Add to history
        if (action.payload.system) {
          state.metricsHistory.push(action.payload.system);
          if (state.metricsHistory.length > 100) {
            state.metricsHistory = state.metricsHistory.slice(-100);
          }
        }
      })
      .addCase(fetchMetrics.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { addMetricsToHistory, clearError } = monitoringSlice.actions;
export const selectSystemMetrics = (state: { monitoring: MonitoringState }) => state.monitoring.systemMetrics;
export const selectHealthStatus = (state: { monitoring: MonitoringState }) => state.monitoring.healthStatus;
export const selectMonitoringLoading = (state: { monitoring: MonitoringState }) => state.monitoring.isLoading;
export const selectMonitoringError = (state: { monitoring: MonitoringState }) => state.monitoring.error;
export const selectMetricsHistory = (state: { monitoring: MonitoringState }) => state.monitoring.metricsHistory;

export default monitoringSlice.reducer;

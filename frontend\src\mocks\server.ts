/**
 * Mock Service Worker (MSW) server setup for testing.
 * 
 * This file sets up MSW to intercept and mock API requests during testing.
 */

import { setupServer } from 'msw/node';
import { rest } from 'msw';

// Mock API responses
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'user',
  permissions: ['read', 'write'],
  preferences: {
    theme: 'light',
    language: 'en',
    timezone: 'UTC',
  },
};

const mockTokens = {
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  token_type: 'bearer',
  expires_in: 3600,
};

// Define request handlers
export const handlers = [
  // Authentication endpoints
  rest.post('/api/v1/auth/login', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        ...mockTokens,
        user: mockUser,
      })
    );
  }),

  rest.post('/api/v1/auth/register', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        message: 'User registered successfully',
        user: mockUser,
      })
    );
  }),

  rest.post('/api/v1/auth/refresh', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(mockTokens)
    );
  }),

  rest.get('/api/v1/auth/me', (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res(
        ctx.status(401),
        ctx.json({
          error: {
            code: 'AUTH_ERROR',
            message: 'Authentication required',
          },
        })
      );
    }

    return res(
      ctx.status(200),
      ctx.json(mockUser)
    );
  }),

  rest.put('/api/v1/auth/profile', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        ...mockUser,
        // Merge any updates from the request body
      })
    );
  }),

  // Health check
  rest.get('/health', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        environment: 'test',
      })
    );
  }),

  // API info
  rest.get('/api/v1/', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        name: 'Enterprise AI/ML Platform API',
        version: 'v1',
        description: 'Production-ready API for comprehensive AI/ML operations',
        endpoints: {
          auth: '/api/v1/auth',
          agents: '/api/v1/agents',
          workflows: '/api/v1/workflows',
          models: '/api/v1/models',
          data: '/api/v1/data',
          marketplace: '/api/v1/marketplace',
          monitoring: '/api/v1/monitoring',
        },
      })
    );
  }),

  // ML Models endpoints
  rest.get('/api/v1/models', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          model_name: 'test_model_1',
          metadata: {
            algorithm: 'random_forest_classifier',
            features: ['feature1', 'feature2', 'feature3'],
            target: 'target',
            training_samples: 1000,
            created_at: '2024-01-01T00:00:00Z',
          },
          is_loaded: true,
        },
        {
          model_name: 'test_model_2',
          metadata: {
            algorithm: 'logistic_regression',
            features: ['feature1', 'feature2'],
            target: 'target',
            training_samples: 500,
            created_at: '2024-01-02T00:00:00Z',
          },
          is_loaded: true,
        },
      ])
    );
  }),

  rest.post('/api/v1/models/train', (req, res, ctx) => {
    return res(
      ctx.status(202),
      ctx.json({
        task_id: 'mock-task-id',
        status: 'accepted',
        message: 'Model training started',
      })
    );
  }),

  rest.post('/api/v1/models/:modelName/predict', (req, res, ctx) => {
    const { modelName } = req.params;
    return res(
      ctx.status(200),
      ctx.json({
        model_name: modelName,
        predictions: [0, 1, 0, 1, 1],
        probabilities: [
          [0.8, 0.2],
          [0.3, 0.7],
          [0.9, 0.1],
          [0.4, 0.6],
          [0.2, 0.8],
        ],
        num_samples: 5,
      })
    );
  }),

  // Workflows endpoints
  rest.get('/api/v1/workflows', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          id: '1',
          name: 'Test Workflow',
          description: 'A test workflow',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ])
    );
  }),

  rest.post('/api/v1/workflows', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: '2',
        name: 'New Workflow',
        description: 'A new workflow',
        status: 'draft',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
    );
  }),

  // Agents endpoints
  rest.get('/api/v1/agents', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          id: '1',
          name: 'Test Agent',
          type: 'data_processor',
          status: 'active',
          config: {},
          created_at: '2024-01-01T00:00:00Z',
        },
      ])
    );
  }),

  // Monitoring endpoints
  rest.get('/api/v1/monitoring/health', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        checks: {
          system: { status: 'healthy', cpu_percent: 25.5 },
          database: { status: 'healthy', connections: 'active' },
          ml_service: { status: 'healthy', models_loaded: 2 },
          api: { status: 'healthy', uptime_seconds: 3600 },
        },
      })
    );
  }),

  rest.get('/api/v1/monitoring/metrics', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        timestamp: new Date().toISOString(),
        system: {
          cpu_percent: 25.5,
          memory_percent: 45.2,
          disk_percent: 60.1,
        },
        ml: {
          models_count: { count: 2 },
          predictions: [],
          training: [],
        },
        api: {
          total_requests: 100,
          recent_requests: [],
        },
        uptime_seconds: 3600,
      })
    );
  }),

  // Error handling - catch all unhandled requests
  rest.get('*', (req, res, ctx) => {
    console.warn(`Unhandled GET request to ${req.url}`);
    return res(
      ctx.status(404),
      ctx.json({
        error: {
          code: 'NOT_FOUND',
          message: 'Endpoint not found',
          path: req.url.pathname,
        },
      })
    );
  }),

  rest.post('*', (req, res, ctx) => {
    console.warn(`Unhandled POST request to ${req.url}`);
    return res(
      ctx.status(404),
      ctx.json({
        error: {
          code: 'NOT_FOUND',
          message: 'Endpoint not found',
          path: req.url.pathname,
        },
      })
    );
  }),
];

// Create and export the server
export const server = setupServer(...handlers);

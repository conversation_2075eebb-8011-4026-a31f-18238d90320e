# Enterprise AI/ML Platform - Backend Dependencies
# Production-ready Python packages with pinned versions

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database & ORM
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
alembic==1.13.1
motor==3.3.2
pymongo==4.6.0

# Caching & Message Queue
redis[hiredis]==5.0.1
celery[redis]==5.3.4
kombu==5.3.4

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# HTTP Client & Utilities
httpx==0.25.2
aiofiles==23.2.1
python-dotenv==1.0.0

# Data Processing & ML Core
numpy==1.24.4
pandas==2.1.4
scikit-learn==1.3.2
scipy==1.11.4

# Deep Learning Frameworks
torch==2.1.1
torchvision==0.16.1
torchaudio==2.1.1
tensorflow==2.15.0
tensorflow-probability==0.22.1

# AutoML & Advanced ML
autogluon==0.8.2
optuna==3.4.0
hyperopt==0.2.7
bayesian-optimization==1.4.3

# Computer Vision
opencv-python==********
Pillow==10.1.0
albumentations==1.3.1
timm==0.9.12

# Natural Language Processing
transformers==4.36.2
tokenizers==0.15.0
datasets==2.15.0
sentence-transformers==2.2.2
spacy==3.7.2
nltk==3.8.1

# Generative AI
diffusers==0.24.0
accelerate==0.25.0
xformers==0.0.23

# Time Series
prophet==1.1.5
statsmodels==0.14.0
pmdarima==2.0.4
sktime==0.25.0

# Reinforcement Learning
stable-baselines3==2.2.1
gymnasium==0.29.1
ale-py==0.8.1

# Graph Neural Networks
torch-geometric==2.4.0
networkx==3.2.1
igraph==0.11.3

# Quantum Computing
qiskit==0.45.1
qiskit-aer==0.13.1
qiskit-optimization==0.6.0

# Audio Processing
librosa==0.10.1
soundfile==0.12.1
speechrecognition==3.10.0
pydub==0.25.1

# Data Visualization & Plotting
plotly==5.17.0
matplotlib==3.8.2
seaborn==0.13.0

# Model Interpretability
shap==0.43.0
lime==0.2.0.1

# MLOps & Experiment Tracking
mlflow==2.8.1
wandb==0.16.1
dvc==3.30.3

# Monitoring & Observability
prometheus-client==0.19.0
prometheus-fastapi-instrumentator==6.1.0
structlog==23.2.0

# API Documentation
python-markdown==3.5.1

# Testing & Development
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5
safety==2.3.5

# Performance & Optimization
numba==0.58.1
cython==3.0.6

# Cloud & Storage
boto3==1.34.0
google-cloud-storage==2.10.0
azure-storage-blob==12.19.0

# Utilities
click==8.1.7
tqdm==4.66.1
python-dateutil==2.8.2
pytz==2023.3
requests==2.31.0

# Email
emails==0.6

# Rate Limiting
slowapi==0.1.9

# WebSocket
websockets==12.0

# Image Processing
imageio==2.33.0
scikit-image==0.22.0

# Feature Engineering
feature-engine==1.6.2
category-encoders==2.6.3

# Model Serving
onnx==1.15.0
onnxruntime==1.16.3

# Federated Learning
flower==1.6.0

# Edge AI
tensorrt==8.6.1  # NVIDIA TensorRT (if available)
openvino==2023.2.0  # Intel OpenVINO

# Additional Scientific Computing
sympy==1.12
joblib==1.3.2
dask==2023.12.0

# Configuration Management
dynaconf==3.2.4

# Async Database Drivers
aiosqlite==0.19.0
aiomysql==0.2.0

# Serialization
orjson==3.9.10
msgpack==1.0.7

# Validation
cerberus==1.3.5
marshmallow==3.20.1

# Caching
diskcache==5.6.3

# Compression
lz4==4.3.2
zstandard==0.22.0

# Profiling
py-spy==0.3.14
memory-profiler==0.61.0

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

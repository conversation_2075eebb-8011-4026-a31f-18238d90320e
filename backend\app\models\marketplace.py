"""
Marketplace Database Models
===========================

SQLAlchemy models for marketplace functionality.
"""

from sqlalchemy import Column, String, Text, DateTime, Float, Integer, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()


class MarketplaceItem(Base):
    """Marketplace item model for storing items available for purchase."""
    
    __tablename__ = "marketplace_items"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Item details
    item_type = Column(String(50), nullable=False)  # model, workflow, dataset, agent
    category = Column(String(100))
    tags = Column(JSON)  # List of tags
    
    # Pricing
    pricing_model = Column(String(50), nullable=False)  # free, one_time, subscription, usage_based
    price = Column(Float, default=0.0)
    currency = Column(String(3), default="USD")
    
    # Publisher
    publisher_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Content
    content_url = Column(String(500))  # URL to download content
    demo_url = Column(String(500))  # URL to demo/preview
    documentation_url = Column(String(500))
    
    # Metadata
    metadata = Column(JSON)  # Additional metadata
    requirements = Column(JSON)  # System requirements
    
    # Status
    status = Column(String(50), default="draft")  # draft, pending_review, approved, published, suspended
    is_featured = Column(Boolean, default=False)
    
    # Statistics
    view_count = Column(Integer, default=0)
    download_count = Column(Integer, default=0)
    purchase_count = Column(Integer, default=0)
    
    # Ratings
    average_rating = Column(Float, default=0.0)
    rating_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = Column(DateTime)
    
    # Relationships
    publisher = relationship("User", back_populates="marketplace_items")
    transactions = relationship("Transaction", back_populates="item")
    reviews = relationship("Review", back_populates="item")
    
    def __repr__(self):
        return f"<MarketplaceItem(id='{self.id}', title='{self.title}')>"
    
    def update_rating(self):
        """Update average rating based on reviews."""
        if self.reviews:
            total_rating = sum(review.rating for review in self.reviews)
            self.average_rating = total_rating / len(self.reviews)
            self.rating_count = len(self.reviews)


class Transaction(Base):
    """Transaction model for marketplace purchases."""
    
    __tablename__ = "transactions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Transaction details
    buyer_id = Column(String, ForeignKey("users.id"), nullable=False)
    seller_id = Column(String, ForeignKey("users.id"), nullable=False)
    item_id = Column(String, ForeignKey("marketplace_items.id"), nullable=False)
    
    # Payment details
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default="USD")
    payment_method = Column(String(50))
    payment_intent_id = Column(String(255))  # Stripe payment intent ID
    
    # Revenue sharing
    platform_fee = Column(Float, default=0.0)
    seller_amount = Column(Float, default=0.0)
    
    # Status
    status = Column(String(50), default="pending")  # pending, completed, failed, refunded
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # Relationships
    buyer = relationship("User", foreign_keys=[buyer_id])
    seller = relationship("User", foreign_keys=[seller_id])
    item = relationship("MarketplaceItem", back_populates="transactions")
    
    def __repr__(self):
        return f"<Transaction(id='{self.id}', amount='{self.amount}')>"


class Review(Base):
    """Review model for marketplace item reviews."""
    
    __tablename__ = "reviews"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Review details
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    item_id = Column(String, ForeignKey("marketplace_items.id"), nullable=False)
    
    # Review content
    rating = Column(Integer, nullable=False)  # 1-5 stars
    title = Column(String(255))
    comment = Column(Text)
    
    # Metadata
    is_verified_purchase = Column(Boolean, default=False)
    helpful_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User")
    item = relationship("MarketplaceItem", back_populates="reviews")
    
    def __repr__(self):
        return f"<Review(id='{self.id}', rating='{self.rating}')>"

/**
 * Data slice for Redux store.
 * 
 * Manages data sources, datasets, and data processing operations.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface Dataset {
  id: string;
  name: string;
  description: string;
  type: 'csv' | 'json' | 'database' | 'api';
  size: number;
  rows: number;
  columns: number;
  created_at: string;
  updated_at: string;
  schema: Array<{
    name: string;
    type: string;
    nullable: boolean;
  }>;
}

export interface DataState {
  datasets: Dataset[];
  currentDataset: Dataset | null;
  isLoading: boolean;
  error: string | null;
  uploadProgress: number;
}

const initialState: DataState = {
  datasets: [],
  currentDataset: null,
  isLoading: false,
  error: null,
  uploadProgress: 0,
};

// Async thunks
export const fetchDatasets = createAsyncThunk(
  'data/fetchDatasets',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/data/datasets');
      if (!response.ok) throw new Error('Failed to fetch datasets');
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

const dataSlice = createSlice({
  name: 'data',
  initialState,
  reducers: {
    setCurrentDataset: (state, action: PayloadAction<Dataset | null>) => {
      state.currentDataset = action.payload;
    },
    setUploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDatasets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDatasets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.datasets = action.payload;
      })
      .addCase(fetchDatasets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setCurrentDataset, setUploadProgress, clearError } = dataSlice.actions;
export const selectDatasets = (state: { data: DataState }) => state.data.datasets;
export const selectCurrentDataset = (state: { data: DataState }) => state.data.currentDataset;
export const selectDataLoading = (state: { data: DataState }) => state.data.isLoading;
export const selectDataError = (state: { data: DataState }) => state.data.error;

export default dataSlice.reducer;

{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "auto", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "overrides": [{"files": "*.json", "options": {"printWidth": 80}}, {"files": "*.md", "options": {"proseWrap": "always", "printWidth": 80}}, {"files": "*.yaml", "options": {"tabWidth": 2}}, {"files": "*.yml", "options": {"tabWidth": 2}}]}
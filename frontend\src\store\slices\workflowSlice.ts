/**
 * Workflow slice for Redux store.
 * 
 * Manages workflow state including workflow creation, execution,
 * and management.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface WorkflowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'failed';
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  created_at: string;
  updated_at: string;
  created_by: string;
  tags: string[];
  version: number;
}

export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at?: string;
  progress: number;
  logs: Array<{
    timestamp: string;
    level: 'info' | 'warning' | 'error';
    message: string;
    node_id?: string;
  }>;
}

export interface WorkflowState {
  workflows: Workflow[];
  currentWorkflow: Workflow | null;
  executions: WorkflowExecution[];
  currentExecution: WorkflowExecution | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    status: string[];
    tags: string[];
    created_by: string[];
  };
}

// Initial state
const initialState: WorkflowState = {
  workflows: [],
  currentWorkflow: null,
  executions: [],
  currentExecution: null,
  isLoading: false,
  error: null,
  filters: {
    status: [],
    tags: [],
    created_by: [],
  },
};

// Async thunks
export const fetchWorkflows = createAsyncThunk(
  'workflow/fetchWorkflows',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/workflows');
      if (!response.ok) {
        throw new Error('Failed to fetch workflows');
      }
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

export const createWorkflow = createAsyncThunk(
  'workflow/createWorkflow',
  async (workflowData: Partial<Workflow>, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/workflows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(workflowData),
      });
      if (!response.ok) {
        throw new Error('Failed to create workflow');
      }
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

export const updateWorkflow = createAsyncThunk(
  'workflow/updateWorkflow',
  async ({ id, updates }: { id: string; updates: Partial<Workflow> }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/v1/workflows/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });
      if (!response.ok) {
        throw new Error('Failed to update workflow');
      }
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

export const deleteWorkflow = createAsyncThunk(
  'workflow/deleteWorkflow',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/v1/workflows/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        throw new Error('Failed to delete workflow');
      }
      return id;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

export const executeWorkflow = createAsyncThunk(
  'workflow/executeWorkflow',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/v1/workflows/${id}/execute`, {
        method: 'POST',
      });
      if (!response.ok) {
        throw new Error('Failed to execute workflow');
      }
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

// Workflow slice
const workflowSlice = createSlice({
  name: 'workflow',
  initialState,
  reducers: {
    setCurrentWorkflow: (state, action: PayloadAction<Workflow | null>) => {
      state.currentWorkflow = action.payload;
    },
    setCurrentExecution: (state, action: PayloadAction<WorkflowExecution | null>) => {
      state.currentExecution = action.payload;
    },
    updateWorkflowNodes: (state, action: PayloadAction<WorkflowNode[]>) => {
      if (state.currentWorkflow) {
        state.currentWorkflow.nodes = action.payload;
      }
    },
    updateWorkflowEdges: (state, action: PayloadAction<WorkflowEdge[]>) => {
      if (state.currentWorkflow) {
        state.currentWorkflow.edges = action.payload;
      }
    },
    addWorkflowNode: (state, action: PayloadAction<WorkflowNode>) => {
      if (state.currentWorkflow) {
        state.currentWorkflow.nodes.push(action.payload);
      }
    },
    removeWorkflowNode: (state, action: PayloadAction<string>) => {
      if (state.currentWorkflow) {
        state.currentWorkflow.nodes = state.currentWorkflow.nodes.filter(
          node => node.id !== action.payload
        );
        // Also remove edges connected to this node
        state.currentWorkflow.edges = state.currentWorkflow.edges.filter(
          edge => edge.source !== action.payload && edge.target !== action.payload
        );
      }
    },
    addWorkflowEdge: (state, action: PayloadAction<WorkflowEdge>) => {
      if (state.currentWorkflow) {
        state.currentWorkflow.edges.push(action.payload);
      }
    },
    removeWorkflowEdge: (state, action: PayloadAction<string>) => {
      if (state.currentWorkflow) {
        state.currentWorkflow.edges = state.currentWorkflow.edges.filter(
          edge => edge.id !== action.payload
        );
      }
    },
    setFilters: (state, action: PayloadAction<Partial<WorkflowState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch workflows
    builder
      .addCase(fetchWorkflows.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWorkflows.fulfilled, (state, action) => {
        state.isLoading = false;
        state.workflows = action.payload;
      })
      .addCase(fetchWorkflows.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create workflow
    builder
      .addCase(createWorkflow.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWorkflow.fulfilled, (state, action) => {
        state.isLoading = false;
        state.workflows.push(action.payload);
        state.currentWorkflow = action.payload;
      })
      .addCase(createWorkflow.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update workflow
    builder
      .addCase(updateWorkflow.fulfilled, (state, action) => {
        const index = state.workflows.findIndex(w => w.id === action.payload.id);
        if (index !== -1) {
          state.workflows[index] = action.payload;
        }
        if (state.currentWorkflow?.id === action.payload.id) {
          state.currentWorkflow = action.payload;
        }
      })
      .addCase(updateWorkflow.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Delete workflow
    builder
      .addCase(deleteWorkflow.fulfilled, (state, action) => {
        state.workflows = state.workflows.filter(w => w.id !== action.payload);
        if (state.currentWorkflow?.id === action.payload) {
          state.currentWorkflow = null;
        }
      })
      .addCase(deleteWorkflow.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Execute workflow
    builder
      .addCase(executeWorkflow.fulfilled, (state, action) => {
        state.executions.push(action.payload);
        state.currentExecution = action.payload;
      })
      .addCase(executeWorkflow.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  setCurrentWorkflow,
  setCurrentExecution,
  updateWorkflowNodes,
  updateWorkflowEdges,
  addWorkflowNode,
  removeWorkflowNode,
  addWorkflowEdge,
  removeWorkflowEdge,
  setFilters,
  clearError,
} = workflowSlice.actions;

// Export selectors
export const selectWorkflows = (state: { workflow: WorkflowState }) => state.workflow.workflows;
export const selectCurrentWorkflow = (state: { workflow: WorkflowState }) => state.workflow.currentWorkflow;
export const selectWorkflowExecutions = (state: { workflow: WorkflowState }) => state.workflow.executions;
export const selectCurrentExecution = (state: { workflow: WorkflowState }) => state.workflow.currentExecution;
export const selectWorkflowLoading = (state: { workflow: WorkflowState }) => state.workflow.isLoading;
export const selectWorkflowError = (state: { workflow: WorkflowState }) => state.workflow.error;
export const selectWorkflowFilters = (state: { workflow: WorkflowState }) => state.workflow.filters;

// Export reducer
export default workflowSlice.reducer;

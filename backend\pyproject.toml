[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "enterprise-aiml-platform"
version = "2.0.0"
description = "Enterprise AI/ML Platform - Production-ready no-code AI/ML platform"
readme = "README.md"
license = "MIT"
requires-python = ">=3.11"
authors = [
    { name = "Enterprise AI/ML Platform Team", email = "<EMAIL>" },
]
keywords = [
    "ai",
    "ml",
    "machine-learning",
    "artificial-intelligence",
    "no-code",
    "platform",
    "enterprise",
    "fastapi",
    "python",
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Framework :: FastAPI",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.12.1",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.9",
    "redis>=5.0.1",
    "celery>=5.3.4",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-dotenv>=1.0.0",
    "httpx>=0.25.2",
    "aiofiles>=23.2.1",
    "jinja2>=3.1.2",
    "pandas>=2.1.4",
    "numpy>=1.25.2",
    "scikit-learn>=1.3.2",
    "xgboost>=2.0.2",
    "lightgbm>=4.1.0",
    "catboost>=1.2.2",
    "autogluon>=1.0.0",
    "torch>=2.1.1",
    "torchvision>=0.16.1",
    "transformers>=4.36.2",
    "datasets>=2.15.0",
    "accelerate>=0.25.0",
    "diffusers>=0.24.0",
    "opencv-python>=********",
    "pillow>=10.1.0",
    "matplotlib>=3.8.2",
    "seaborn>=0.13.0",
    "plotly>=5.17.0",
    "streamlit>=1.28.2",
    "gradio>=4.8.0",
    "prometheus-client>=0.19.0",
    "structlog>=23.2.0",
    "python-json-logger>=2.0.7",
    "sentry-sdk[fastapi]>=1.38.0",
    "boto3>=1.34.0",
    "azure-storage-blob>=12.19.0",
    "google-cloud-storage>=2.10.0",
    "kubernetes>=28.1.0",
    "docker>=6.1.3",
    "pyyaml>=6.0.1",
    "click>=8.1.7",
    "rich>=13.7.0",
    "typer>=0.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-xdist>=3.3.1",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "bandit>=1.7.5",
    "pre-commit>=3.5.0",
    "ipython>=8.17.2",
    "jupyter>=1.0.0",
    "notebook>=7.0.6",
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=1.3.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
    "httpie>=3.2.2",
    "safety>=2.3.5",
    "watchdog>=3.0.0",
]

test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.2",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
]

docs = [
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=1.3.0",
    "sphinx-autodoc-typehints>=1.25.2",
]

[project.urls]
Homepage = "https://github.com/aiml-platform/platform"
Documentation = "https://docs.aiml-platform.com"
Repository = "https://github.com/aiml-platform/platform.git"
"Bug Tracker" = "https://github.com/aiml-platform/platform/issues"
Changelog = "https://github.com/aiml-platform/platform/blob/main/CHANGELOG.md"

[project.scripts]
aiml-platform = "app.cli:main"

[tool.hatch.version]
path = "app/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
  | alembic
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
skip_gitignore = true
skip_glob = ["migrations/*", "alembic/*"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "celery.*",
    "redis.*",
    "sklearn.*",
    "xgboost.*",
    "lightgbm.*",
    "catboost.*",
    "autogluon.*",
    "torch.*",
    "torchvision.*",
    "transformers.*",
    "datasets.*",
    "diffusers.*",
    "cv2.*",
    "matplotlib.*",
    "seaborn.*",
    "plotly.*",
    "streamlit.*",
    "gradio.*",
    "boto3.*",
    "azure.*",
    "google.*",
    "kubernetes.*",
    "docker.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests",
    "auth: Tests requiring authentication",
    "ml: Machine learning related tests",
    "api: API endpoint tests",
    "database: Database related tests",
    "celery: Celery task tests",
    "websocket: WebSocket tests",
]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/alembic/*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "migrations", "alembic"]
skips = ["B101", "B601"]

/**
 * Model slice for Redux store.
 * 
 * Manages ML model state including model training, deployment,
 * and inference operations.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface Model {
  id: string;
  name: string;
  algorithm: string;
  status: 'training' | 'trained' | 'deployed' | 'failed';
  accuracy?: number;
  created_at: string;
  updated_at: string;
  metadata: {
    features: string[];
    target: string;
    training_samples: number;
    config: Record<string, any>;
  };
}

export interface ModelState {
  models: Model[];
  currentModel: Model | null;
  isLoading: boolean;
  error: string | null;
  trainingProgress: Record<string, number>;
}

const initialState: ModelState = {
  models: [],
  currentModel: null,
  isLoading: false,
  error: null,
  trainingProgress: {},
};

// Async thunks
export const fetchModels = createAsyncThunk(
  'model/fetchModels',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/models');
      if (!response.ok) throw new Error('Failed to fetch models');
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

export const trainModel = createAsyncThunk(
  'model/trainModel',
  async (modelConfig: any, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/models/train', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(modelConfig),
      });
      if (!response.ok) throw new Error('Failed to start model training');
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

const modelSlice = createSlice({
  name: 'model',
  initialState,
  reducers: {
    setCurrentModel: (state, action: PayloadAction<Model | null>) => {
      state.currentModel = action.payload;
    },
    updateTrainingProgress: (state, action: PayloadAction<{ modelId: string; progress: number }>) => {
      state.trainingProgress[action.payload.modelId] = action.payload.progress;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchModels.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchModels.fulfilled, (state, action) => {
        state.isLoading = false;
        state.models = action.payload;
      })
      .addCase(fetchModels.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(trainModel.fulfilled, (state, action) => {
        // Handle training start
        state.error = null;
      })
      .addCase(trainModel.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { setCurrentModel, updateTrainingProgress, clearError } = modelSlice.actions;
export const selectModels = (state: { model: ModelState }) => state.model.models;
export const selectCurrentModel = (state: { model: ModelState }) => state.model.currentModel;
export const selectModelLoading = (state: { model: ModelState }) => state.model.isLoading;
export const selectModelError = (state: { model: ModelState }) => state.model.error;

export default modelSlice.reducer;

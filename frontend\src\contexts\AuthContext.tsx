/**
 * Authentication Context for the Enterprise AI/ML Platform.
 * 
 * Provides authentication state and methods throughout the application,
 * including automatic token refresh and session management.
 */

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAppDispatch, useAppSelector } from '../store/store';
import {
  fetchUserProfile,
  refreshAccessToken,
  logout,
  selectAuth,
  selectIsAuthenticated,
  selectUser,
} from '../store/slices/authSlice';

// Types
interface AuthContextType {
  isAuthenticated: boolean;
  user: any;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const auth = useAppSelector(selectAuth);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);

  // Initialize authentication on app start
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('token');
      
      if (token) {
        try {
          // Try to fetch user profile with existing token
          await dispatch(fetchUserProfile()).unwrap();
        } catch (error) {
          // If profile fetch fails, try to refresh token
          try {
            await dispatch(refreshAccessToken()).unwrap();
            await dispatch(fetchUserProfile()).unwrap();
          } catch (refreshError) {
            // If refresh also fails, logout user
            dispatch(logout());
          }
        }
      }
    };

    initializeAuth();
  }, [dispatch]);

  // Set up automatic token refresh
  useEffect(() => {
    if (!isAuthenticated || !auth.token) return;

    // Decode token to get expiration time
    const getTokenExpiration = (token: string): number => {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.exp * 1000; // Convert to milliseconds
      } catch {
        return 0;
      }
    };

    const tokenExpiration = getTokenExpiration(auth.token);
    const now = Date.now();
    const timeUntilExpiry = tokenExpiration - now;
    
    // Refresh token 5 minutes before expiry
    const refreshTime = timeUntilExpiry - 5 * 60 * 1000;

    if (refreshTime > 0) {
      const refreshTimer = setTimeout(async () => {
        try {
          await dispatch(refreshAccessToken()).unwrap();
        } catch (error) {
          console.error('Token refresh failed:', error);
          dispatch(logout());
        }
      }, refreshTime);

      return () => clearTimeout(refreshTimer);
    } else if (timeUntilExpiry <= 0) {
      // Token is already expired
      dispatch(logout());
    }
  }, [auth.token, isAuthenticated, dispatch]);

  // Auth methods
  const login = async (email: string, password: string): Promise<void> => {
    const { loginUser } = await import('../store/slices/authSlice');
    await dispatch(loginUser({ email, password })).unwrap();
  };

  const handleLogout = (): void => {
    dispatch(logout());
  };

  const refreshToken = async (): Promise<void> => {
    await dispatch(refreshAccessToken()).unwrap();
  };

  // Context value
  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    login,
    logout: handleLogout,
    refreshToken,
    isLoading: auth.isLoading,
    error: auth.error,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

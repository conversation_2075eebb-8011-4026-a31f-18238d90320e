/**
 * Redux store configuration for the Enterprise AI/ML Platform.
 * 
 * This file sets up the Redux store with Redux Toolkit, including
 * middleware, dev tools, and slice reducers.
 */

import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Import slice reducers
import authReducer from './slices/authSlice';
import uiReducer from './slices/uiSlice';
import workflowReducer from './slices/workflowSlice';
import modelReducer from './slices/modelSlice';
import dataReducer from './slices/dataSlice';
import agentReducer from './slices/agentSlice';
import monitoringReducer from './slices/monitoringSlice';

// Configure the store
export const store = configureStore({
  reducer: {
    auth: authReducer,
    ui: uiReducer,
    workflow: workflowReducer,
    model: modelReducer,
    data: dataReducer,
    agent: agentReducer,
    monitoring: monitoringReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Export store for use in other files
export default store;

/**
 * Node Library Component for NeuroFlowAI
 * ======================================
 * 
 * Draggable library of workflow nodes organized by category.
 * Includes data sources, preprocessing, models, agents, and outputs.
 */

import React, { useState } from 'react';
import { 
  DatabaseIcon, 
  CogIcon, 
  CpuChipIcon, 
  UserGroupIcon, 
  DocumentArrowDownIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

// Node categories and definitions
const nodeCategories = {
  dataSources: {
    title: 'Data Sources',
    icon: DatabaseIcon,
    color: 'blue',
    nodes: [
      {
        type: 'dataSource',
        label: 'CSV Upload',
        description: 'Upload and process CSV files',
        icon: '📊',
        agent: 'architect',
        defaultConfig: {
          source: 'upload',
          format: 'csv',
          hasHeader: true,
          delimiter: ',',
        },
      },
      {
        type: 'dataSource',
        label: 'Database Query',
        description: 'Connect to SQL databases',
        icon: '🗄️',
        agent: 'architect',
        defaultConfig: {
          source: 'database',
          connectionType: 'postgresql',
          query: 'SELECT * FROM table',
        },
      },
      {
        type: 'dataSource',
        label: 'API Endpoint',
        description: 'Fetch data from REST APIs',
        icon: '🌐',
        agent: 'architect',
        defaultConfig: {
          source: 'api',
          method: 'GET',
          url: '',
          headers: {},
        },
      },
      {
        type: 'dataSource',
        label: 'File Storage',
        description: 'Load from S3, GCS, or Azure',
        icon: '☁️',
        agent: 'architect',
        defaultConfig: {
          source: 'cloud',
          provider: 's3',
          bucket: '',
          path: '',
        },
      },
    ],
  },
  preprocessing: {
    title: 'Data Preprocessing',
    icon: CogIcon,
    color: 'green',
    nodes: [
      {
        type: 'preprocessing',
        label: 'Data Cleaning',
        description: 'Handle missing values and outliers',
        icon: '🧹',
        agent: 'engineer',
        defaultConfig: {
          operation: 'cleaning',
          handleMissing: 'impute',
          removeOutliers: true,
        },
      },
      {
        type: 'preprocessing',
        label: 'Feature Engineering',
        description: 'Create and transform features',
        icon: '⚙️',
        agent: 'engineer',
        defaultConfig: {
          operation: 'feature_engineering',
          autoGenerate: true,
          polynomialFeatures: false,
        },
      },
      {
        type: 'preprocessing',
        label: 'Data Scaling',
        description: 'Normalize and standardize data',
        icon: '📏',
        agent: 'engineer',
        defaultConfig: {
          operation: 'scaling',
          method: 'standard',
          featureRange: [0, 1],
        },
      },
      {
        type: 'preprocessing',
        label: 'Feature Selection',
        description: 'Select most important features',
        icon: '🎯',
        agent: 'engineer',
        defaultConfig: {
          operation: 'feature_selection',
          method: 'mutual_info',
          numFeatures: 10,
        },
      },
    ],
  },
  models: {
    title: 'AI/ML Models',
    icon: CpuChipIcon,
    color: 'purple',
    nodes: [
      {
        type: 'model',
        label: 'AutoML',
        description: 'Automated model selection and tuning',
        icon: '🤖',
        agent: 'engineer',
        defaultConfig: {
          modelType: 'automl',
          framework: 'autogluon',
          timeLimit: 3600,
        },
      },
      {
        type: 'model',
        label: 'Neural Network',
        description: 'Deep learning with PyTorch/TensorFlow',
        icon: '🧠',
        agent: 'trainer',
        defaultConfig: {
          modelType: 'neural_network',
          framework: 'pytorch',
          architecture: 'mlp',
        },
      },
      {
        type: 'model',
        label: 'Transformer',
        description: 'Large language models and transformers',
        icon: '🔤',
        agent: 'trainer',
        defaultConfig: {
          modelType: 'transformer',
          baseModel: 'bert-base-uncased',
          taskType: 'classification',
        },
      },
      {
        type: 'model',
        label: 'Computer Vision',
        description: 'Image classification and detection',
        icon: '👁️',
        agent: 'trainer',
        defaultConfig: {
          modelType: 'vision',
          architecture: 'resnet50',
          taskType: 'classification',
        },
      },
      {
        type: 'model',
        label: 'Time Series',
        description: 'Forecasting and anomaly detection',
        icon: '📈',
        agent: 'trainer',
        defaultConfig: {
          modelType: 'time_series',
          algorithm: 'prophet',
          horizon: 30,
        },
      },
    ],
  },
  agents: {
    title: 'AI Agents',
    icon: UserGroupIcon,
    color: 'indigo',
    nodes: [
      {
        type: 'agent',
        label: 'Architect Agent',
        description: 'Design optimal model architectures',
        icon: '🏗️',
        agent: 'architect',
        defaultConfig: {
          agentType: 'architect',
          task: 'pipeline_design',
          autoOptimize: true,
        },
      },
      {
        type: 'agent',
        label: 'Engineer Agent',
        description: 'Feature engineering and optimization',
        icon: '👨‍💻',
        agent: 'engineer',
        defaultConfig: {
          agentType: 'engineer',
          task: 'feature_engineering',
          autoTune: true,
        },
      },
      {
        type: 'agent',
        label: 'Trainer Agent',
        description: 'GPU training orchestration',
        icon: '🏋️',
        agent: 'trainer',
        defaultConfig: {
          agentType: 'trainer',
          task: 'model_training',
          gpuType: 'auto',
        },
      },
      {
        type: 'agent',
        label: 'DevOps Agent',
        description: 'Deployment and scaling',
        icon: '🚀',
        agent: 'devops',
        defaultConfig: {
          agentType: 'devops',
          task: 'deployment',
          environment: 'staging',
        },
      },
    ],
  },
  outputs: {
    title: 'Outputs & Deployment',
    icon: DocumentArrowDownIcon,
    color: 'orange',
    nodes: [
      {
        type: 'output',
        label: 'Model Export',
        description: 'Export trained models',
        icon: '📦',
        agent: 'devops',
        defaultConfig: {
          outputType: 'model_export',
          format: 'onnx',
          includeMetadata: true,
        },
      },
      {
        type: 'output',
        label: 'API Deployment',
        description: 'Deploy as REST API',
        icon: '🔌',
        agent: 'devops',
        defaultConfig: {
          outputType: 'api_deployment',
          environment: 'production',
          autoScale: true,
        },
      },
      {
        type: 'output',
        label: 'Batch Predictions',
        description: 'Generate batch predictions',
        icon: '📊',
        agent: 'devops',
        defaultConfig: {
          outputType: 'batch_predictions',
          format: 'csv',
          schedule: 'daily',
        },
      },
      {
        type: 'output',
        label: 'Dashboard',
        description: 'Create monitoring dashboard',
        icon: '📈',
        agent: 'devops',
        defaultConfig: {
          outputType: 'dashboard',
          metrics: ['accuracy', 'latency', 'throughput'],
          refreshInterval: 60,
        },
      },
    ],
  },
};

interface NodeLibraryProps {
  onNodeSelect?: (nodeType: string, config: any) => void;
}

export const NodeLibrary: React.FC<NodeLibraryProps> = ({ onNodeSelect }) => {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['dataSources', 'models'])
  );
  const [searchTerm, setSearchTerm] = useState('');

  // Toggle category expansion
  const toggleCategory = (categoryKey: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryKey)) {
      newExpanded.delete(categoryKey);
    } else {
      newExpanded.add(categoryKey);
    }
    setExpandedCategories(newExpanded);
  };

  // Handle drag start
  const onDragStart = (event: React.DragEvent, nodeType: string, nodeConfig: any) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.setData('application/nodeconfig', JSON.stringify(nodeConfig));
    event.dataTransfer.effectAllowed = 'move';
  };

  // Filter nodes based on search
  const filterNodes = (nodes: any[]) => {
    if (!searchTerm) return nodes;
    return nodes.filter(node => 
      node.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  // Get color classes for categories
  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-50 border-blue-200 text-blue-800',
      green: 'bg-green-50 border-green-200 text-green-800',
      purple: 'bg-purple-50 border-purple-200 text-purple-800',
      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-800',
      orange: 'bg-orange-50 border-orange-200 text-orange-800',
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Node Library</h2>
        
        {/* Search */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search nodes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="flex-1 overflow-y-auto">
        {Object.entries(nodeCategories).map(([categoryKey, category]) => {
          const isExpanded = expandedCategories.has(categoryKey);
          const filteredNodes = filterNodes(category.nodes);
          
          // Hide category if no nodes match search
          if (searchTerm && filteredNodes.length === 0) {
            return null;
          }

          return (
            <div key={categoryKey} className="border-b border-gray-100">
              {/* Category Header */}
              <button
                onClick={() => toggleCategory(categoryKey)}
                className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <category.icon className="h-5 w-5 text-gray-600" />
                  <span className="font-medium text-gray-900">{category.title}</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    {filteredNodes.length}
                  </span>
                </div>
                {isExpanded ? (
                  <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                )}
              </button>

              {/* Category Nodes */}
              {isExpanded && (
                <div className="pb-2">
                  {filteredNodes.map((node, index) => (
                    <div
                      key={`${categoryKey}-${index}`}
                      draggable
                      onDragStart={(e) => onDragStart(e, node.type, node)}
                      onClick={() => onNodeSelect?.(node.type, node)}
                      className={`mx-3 mb-2 p-3 rounded-lg border-2 border-dashed cursor-move hover:shadow-md transition-all ${getColorClasses(category.color)}`}
                    >
                      <div className="flex items-start space-x-3">
                        <span className="text-2xl">{node.icon}</span>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm truncate">{node.label}</h4>
                          <p className="text-xs opacity-75 mt-1 line-clamp-2">{node.description}</p>
                          <div className="flex items-center mt-2 space-x-2">
                            <span className="text-xs bg-white bg-opacity-50 px-2 py-1 rounded">
                              {node.agent}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <p className="text-xs text-gray-600 text-center">
          Drag nodes to the canvas to build your AI workflow
        </p>
      </div>
    </div>
  );
};

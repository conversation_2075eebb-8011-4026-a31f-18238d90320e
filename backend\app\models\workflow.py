"""
Workflow Database Models
========================

SQLAlchemy models for workflow management.
"""

from sqlalchemy import Column, String, Text, DateTime, Float, Integer, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()


class Workflow(Base):
    """Workflow model for storing workflow definitions."""
    
    __tablename__ = "workflows"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Workflow definition
    definition = Column(JSON, nullable=False)  # Workflow graph/DAG
    version = Column(String(50), default="1.0.0")
    
    # Metadata
    category = Column(String(100))
    tags = Column(JSON)  # List of tags
    
    # Ownership
    owner_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Status
    status = Column(String(50), default="draft")  # draft, published, deprecated
    is_public = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = Column(DateTime)
    
    # Performance metrics
    avg_execution_time = Column(Float, default=0.0)
    success_rate = Column(Float, default=0.0)
    total_executions = Column(Integer, default=0)
    
    # Relationships
    owner = relationship("User", back_populates="workflows")
    executions = relationship("WorkflowExecution", back_populates="workflow")
    
    def __repr__(self):
        return f"<Workflow(id='{self.id}', name='{self.name}')>"


class WorkflowExecution(Base):
    """Workflow execution model for tracking workflow runs."""
    
    __tablename__ = "workflow_executions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    workflow_id = Column(String, ForeignKey("workflows.id"), nullable=False)
    
    # Execution details
    status = Column(String(50), default="pending")  # pending, running, completed, failed, cancelled
    progress = Column(Float, default=0.0)  # 0.0 to 1.0
    current_stage = Column(String(255))
    
    # Input/Output
    inputs = Column(JSON)
    outputs = Column(JSON)
    
    # Execution metadata
    executor_id = Column(String, ForeignKey("users.id"))
    execution_config = Column(JSON)
    
    # Timing
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    duration_seconds = Column(Float)
    
    # Resource usage
    cpu_usage = Column(Float)
    memory_usage = Column(Float)
    gpu_usage = Column(Float)
    
    # Cost tracking
    cost_usd = Column(Float, default=0.0)
    
    # Error handling
    error_message = Column(Text)
    error_details = Column(JSON)
    
    # Agents involved
    agents_used = Column(JSON)  # List of agent IDs
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    workflow = relationship("Workflow", back_populates="executions")
    executor = relationship("User")
    
    def __repr__(self):
        return f"<WorkflowExecution(id='{self.id}', status='{self.status}')>"
    
    @property
    def is_running(self):
        """Check if execution is currently running."""
        return self.status in ["pending", "running"]
    
    @property
    def is_completed(self):
        """Check if execution is completed (success or failure)."""
        return self.status in ["completed", "failed", "cancelled"]
    
    def calculate_duration(self):
        """Calculate execution duration."""
        if self.started_at and self.completed_at:
            delta = self.completed_at - self.started_at
            self.duration_seconds = delta.total_seconds()
        return self.duration_seconds

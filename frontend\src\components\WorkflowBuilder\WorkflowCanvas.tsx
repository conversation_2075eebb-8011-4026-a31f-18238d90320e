/**
 * Workflow Canvas Component for NeuroFlowAI
 * =========================================
 * 
 * Main canvas component for the visual workflow builder.
 * Supports drag-and-drop, node connections, and real-time execution.
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  ReactFlowProvider,
  ReactFlowInstance,
  NodeTypes,
  EdgeTypes,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { NodeLibrary } from './NodeLibrary';
import { NodeInspector } from './NodeInspector';
import { WorkflowToolbar } from './WorkflowToolbar';
import { ExecutionPanel } from './ExecutionPanel';

// Custom node types
import { DataSourceNode } from './nodes/DataSourceNode';
import { PreprocessingNode } from './nodes/PreprocessingNode';
import { ModelNode } from './nodes/ModelNode';
import { AgentNode } from './nodes/AgentNode';
import { OutputNode } from './nodes/OutputNode';

// Custom edge types
import { SmartEdge } from './edges/SmartEdge';

// Types
interface WorkflowCanvasProps {
  workflowId?: string;
  onSave?: (workflow: any) => void;
  onExecute?: (workflow: any) => void;
  readOnly?: boolean;
}

interface WorkflowState {
  id: string;
  name: string;
  description: string;
  nodes: Node[];
  edges: Edge[];
  status: 'draft' | 'running' | 'completed' | 'failed';
  executionId?: string;
  metadata: {
    created: string;
    modified: string;
    version: number;
  };
}

// Custom node types mapping
const nodeTypes: NodeTypes = {
  dataSource: DataSourceNode,
  preprocessing: PreprocessingNode,
  model: ModelNode,
  agent: AgentNode,
  output: OutputNode,
};

// Custom edge types mapping
const edgeTypes: EdgeTypes = {
  smart: SmartEdge,
};

// Initial nodes for demo
const initialNodes: Node[] = [
  {
    id: 'start',
    type: 'dataSource',
    position: { x: 100, y: 100 },
    data: {
      label: 'Data Source',
      type: 'csv',
      config: {
        source: 'upload',
        format: 'csv',
        hasHeader: true,
      },
      status: 'ready',
    },
  },
];

const initialEdges: Edge[] = [];

export const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  workflowId,
  onSave,
  onExecute,
  readOnly = false,
}) => {
  // React Flow state
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);

  // UI state
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionLogs, setExecutionLogs] = useState<any[]>([]);
  const [showNodeLibrary, setShowNodeLibrary] = useState(true);
  const [showExecutionPanel, setShowExecutionPanel] = useState(false);

  // Workflow state
  const [workflow, setWorkflow] = useState<WorkflowState>({
    id: workflowId || `workflow_${Date.now()}`,
    name: 'Untitled Workflow',
    description: '',
    nodes: initialNodes,
    edges: initialEdges,
    status: 'draft',
    metadata: {
      created: new Date().toISOString(),
      modified: new Date().toISOString(),
      version: 1,
    },
  });

  // Refs
  const canvasRef = useRef<HTMLDivElement>(null);

  // Handle node connections
  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge = {
        ...params,
        type: 'smart',
        animated: true,
        style: { stroke: '#3b82f6', strokeWidth: 2 },
      };
      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges]
  );

  // Handle drag over for drop functionality
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle node drop from library
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      if (!reactFlowInstance) return;

      const nodeType = event.dataTransfer.getData('application/reactflow');
      const nodeConfig = JSON.parse(event.dataTransfer.getData('application/nodeconfig') || '{}');

      if (!nodeType) return;

      const position = reactFlowInstance.screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });

      const newNode: Node = {
        id: `${nodeType}_${Date.now()}`,
        type: nodeType,
        position,
        data: {
          label: nodeConfig.label || nodeType,
          type: nodeType,
          config: nodeConfig.defaultConfig || {},
          status: 'ready',
          agent: nodeConfig.agent,
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  // Handle node selection
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
  }, []);

  // Handle node updates
  const onNodeUpdate = useCallback(
    (nodeId: string, updates: any) => {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === nodeId
            ? { ...node, data: { ...node.data, ...updates } }
            : node
        )
      );
    },
    [setNodes]
  );

  // Save workflow
  const handleSave = useCallback(() => {
    const updatedWorkflow = {
      ...workflow,
      nodes,
      edges,
      metadata: {
        ...workflow.metadata,
        modified: new Date().toISOString(),
        version: workflow.metadata.version + 1,
      },
    };

    setWorkflow(updatedWorkflow);
    onSave?.(updatedWorkflow);
  }, [workflow, nodes, edges, onSave]);

  // Execute workflow
  const handleExecute = useCallback(async () => {
    if (isExecuting) return;

    setIsExecuting(true);
    setShowExecutionPanel(true);
    setExecutionLogs([]);

    try {
      // Validate workflow
      const validation = validateWorkflow(nodes, edges);
      if (!validation.isValid) {
        throw new Error(`Workflow validation failed: ${validation.errors.join(', ')}`);
      }

      // Convert to execution format
      const executionWorkflow = {
        ...workflow,
        nodes,
        edges,
        status: 'running' as const,
      };

      // Start execution
      const executionId = await executeWorkflow(executionWorkflow);
      
      setWorkflow(prev => ({
        ...prev,
        status: 'running',
        executionId,
      }));

      // Monitor execution
      await monitorExecution(executionId);

    } catch (error) {
      console.error('Workflow execution failed:', error);
      setExecutionLogs(prev => [...prev, {
        timestamp: new Date().toISOString(),
        level: 'error',
        message: `Execution failed: ${error.message}`,
      }]);
      
      setWorkflow(prev => ({ ...prev, status: 'failed' }));
    } finally {
      setIsExecuting(false);
    }
  }, [workflow, nodes, edges, isExecuting]);

  // Validate workflow
  const validateWorkflow = (nodes: Node[], edges: Edge[]) => {
    const errors: string[] = [];

    // Check for at least one data source
    const dataSources = nodes.filter(n => n.type === 'dataSource');
    if (dataSources.length === 0) {
      errors.push('Workflow must have at least one data source');
    }

    // Check for at least one output
    const outputs = nodes.filter(n => n.type === 'output');
    if (outputs.length === 0) {
      errors.push('Workflow must have at least one output');
    }

    // Check for disconnected nodes
    const connectedNodes = new Set();
    edges.forEach(edge => {
      connectedNodes.add(edge.source);
      connectedNodes.add(edge.target);
    });

    const disconnectedNodes = nodes.filter(n => !connectedNodes.has(n.id) && nodes.length > 1);
    if (disconnectedNodes.length > 0) {
      errors.push(`Disconnected nodes found: ${disconnectedNodes.map(n => n.data.label).join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  // Execute workflow via API
  const executeWorkflow = async (workflow: WorkflowState): Promise<string> => {
    const response = await fetch('/api/v1/workflows/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(workflow),
    });

    if (!response.ok) {
      throw new Error(`Failed to start workflow execution: ${response.statusText}`);
    }

    const result = await response.json();
    return result.executionId;
  };

  // Monitor workflow execution
  const monitorExecution = async (executionId: string) => {
    const eventSource = new EventSource(`/api/v1/workflows/${executionId}/stream`);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      setExecutionLogs(prev => [...prev, {
        timestamp: data.timestamp,
        level: data.level || 'info',
        message: data.message,
        nodeId: data.nodeId,
        agent: data.agent,
      }]);

      // Update node status
      if (data.nodeId && data.status) {
        onNodeUpdate(data.nodeId, { status: data.status });
      }

      // Check if execution completed
      if (data.type === 'workflow_completed') {
        setWorkflow(prev => ({ ...prev, status: 'completed' }));
        eventSource.close();
      } else if (data.type === 'workflow_failed') {
        setWorkflow(prev => ({ ...prev, status: 'failed' }));
        eventSource.close();
      }
    };

    eventSource.onerror = (error) => {
      console.error('Execution monitoring error:', error);
      eventSource.close();
    };
  };

  // Auto-save workflow changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (nodes.length > 0 || edges.length > 0) {
        handleSave();
      }
    }, 2000);

    return () => clearTimeout(timer);
  }, [nodes, edges, handleSave]);

  return (
    <div className="workflow-canvas h-screen flex flex-col bg-gray-50">
      {/* Toolbar */}
      <WorkflowToolbar
        workflow={workflow}
        onSave={handleSave}
        onExecute={handleExecute}
        onToggleLibrary={() => setShowNodeLibrary(!showNodeLibrary)}
        onToggleExecution={() => setShowExecutionPanel(!showExecutionPanel)}
        isExecuting={isExecuting}
        readOnly={readOnly}
      />

      <div className="flex-1 flex">
        {/* Node Library */}
        {showNodeLibrary && (
          <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <NodeLibrary />
          </div>
        )}

        {/* Main Canvas */}
        <div className="flex-1 relative">
          <ReactFlowProvider>
            <div ref={canvasRef} className="w-full h-full">
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onInit={setReactFlowInstance}
                onDrop={onDrop}
                onDragOver={onDragOver}
                onNodeClick={onNodeClick}
                nodeTypes={nodeTypes}
                edgeTypes={edgeTypes}
                fitView
                attributionPosition="bottom-left"
                className="bg-gray-50"
              >
                <Background color="#e5e7eb" gap={20} />
                <Controls />
                <MiniMap
                  nodeColor={(node) => {
                    switch (node.data.status) {
                      case 'running': return '#3b82f6';
                      case 'completed': return '#10b981';
                      case 'failed': return '#ef4444';
                      default: return '#6b7280';
                    }
                  }}
                  className="bg-white"
                />
              </ReactFlow>
            </div>
          </ReactFlowProvider>
        </div>

        {/* Node Inspector */}
        {selectedNode && (
          <div className="w-96 bg-white border-l border-gray-200 overflow-y-auto">
            <NodeInspector
              node={selectedNode}
              onUpdate={(updates) => onNodeUpdate(selectedNode.id, updates)}
              onClose={() => setSelectedNode(null)}
            />
          </div>
        )}

        {/* Execution Panel */}
        {showExecutionPanel && (
          <div className="w-96 bg-white border-l border-gray-200">
            <ExecutionPanel
              workflow={workflow}
              logs={executionLogs}
              isExecuting={isExecuting}
              onClose={() => setShowExecutionPanel(false)}
            />
          </div>
        )}
      </div>
    </div>
  );
};

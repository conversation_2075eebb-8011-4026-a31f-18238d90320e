/**
 * Real-time Monitoring Dashboard for NeuroFlowAI
 * ==============================================
 * 
 * Comprehensive dashboard for monitoring AI workflows, models, and system health.
 * Features real-time updates, interactive charts, and intelligent alerts.
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Alert,
  AlertTitle,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  Download as DownloadIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

// Custom components
import { MetricsOverview } from './components/MetricsOverview';
import { WorkflowMonitor } from './components/WorkflowMonitor';
import { ModelPerformance } from './components/ModelPerformance';
import { ResourceUtilization } from './components/ResourceUtilization';
import { AlertsPanel } from './components/AlertsPanel';
import { SystemHealth } from './components/SystemHealth';
import { RealtimeChart } from './components/RealtimeChart';

// Types
interface DashboardMetrics {
  workflows: {
    active: number;
    completed_today: number;
    success_rate: number;
    avg_execution_time: number;
  };
  models: {
    deployed: number;
    total_requests_today: number;
    avg_latency: number;
    error_rate: number;
  };
  resources: {
    gpu_utilization: number;
    cpu_utilization: number;
    memory_usage: number;
    active_instances: number;
  };
  revenue: {
    daily_revenue: number;
    monthly_recurring: number;
    api_calls_today: number;
    active_subscriptions: number;
  };
}

interface Alert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  acknowledged: boolean;
}

interface DashboardProps {
  refreshInterval?: number;
  autoRefresh?: boolean;
  fullscreen?: boolean;
}

export const MonitoringDashboard: React.FC<DashboardProps> = ({
  refreshInterval = 5000,
  autoRefresh = true,
  fullscreen = false,
}) => {
  // State management
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(autoRefresh);
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');

  // Real-time data fetching
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch metrics
      const metricsResponse = await fetch('/api/v1/monitoring/metrics');
      if (!metricsResponse.ok) {
        throw new Error('Failed to fetch metrics');
      }
      const metricsData = await metricsResponse.json();

      // Fetch alerts
      const alertsResponse = await fetch('/api/v1/monitoring/alerts?active=true');
      if (!alertsResponse.ok) {
        throw new Error('Failed to fetch alerts');
      }
      const alertsData = await alertsResponse.json();

      setMetrics(metricsData);
      setAlerts(alertsData.alerts || []);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    fetchDashboardData();

    if (autoRefreshEnabled) {
      const interval = setInterval(fetchDashboardData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchDashboardData, autoRefreshEnabled, refreshInterval]);

  // WebSocket for real-time updates
  useEffect(() => {
    const ws = new WebSocket(`ws://localhost:8000/ws/monitoring`);

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === 'metrics_update') {
        setMetrics(prev => prev ? { ...prev, ...data.metrics } : data.metrics);
      } else if (data.type === 'new_alert') {
        setAlerts(prev => [data.alert, ...prev]);
      } else if (data.type === 'alert_resolved') {
        setAlerts(prev => prev.filter(alert => alert.id !== data.alert_id));
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => {
      ws.close();
    };
  }, []);

  // Manual refresh handler
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // Export data handler
  const handleExportData = async () => {
    try {
      const response = await fetch(`/api/v1/monitoring/export?timeRange=${selectedTimeRange}`);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `monitoring-data-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to export data:', err);
    }
  };

  // Get system status
  const getSystemStatus = () => {
    if (!metrics) return 'unknown';
    
    const criticalAlerts = alerts.filter(alert => alert.severity === 'critical').length;
    const highAlerts = alerts.filter(alert => alert.severity === 'high').length;
    
    if (criticalAlerts > 0) return 'critical';
    if (highAlerts > 0) return 'warning';
    if (metrics.workflows.success_rate < 0.9) return 'warning';
    if (metrics.models.error_rate > 0.05) return 'warning';
    
    return 'healthy';
  };

  const systemStatus = getSystemStatus();

  if (loading && !metrics) {
    return (
      <Box sx={{ width: '100%', mt: 2 }}>
        <LinearProgress />
        <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
          Loading dashboard data...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        <AlertTitle>Dashboard Error</AlertTitle>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3, bgcolor: 'background.default' }}>
      {/* Dashboard Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            NeuroFlowAI Monitoring
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Chip
              icon={
                systemStatus === 'healthy' ? <CheckCircleIcon /> :
                systemStatus === 'warning' ? <WarningIcon /> :
                <ErrorIcon />
              }
              label={`System ${systemStatus.toUpperCase()}`}
              color={
                systemStatus === 'healthy' ? 'success' :
                systemStatus === 'warning' ? 'warning' :
                'error'
              }
              variant="filled"
            />
            <Typography variant="body2" color="text.secondary">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FormControlLabel
            control={
              <Switch
                checked={autoRefreshEnabled}
                onChange={(e) => setAutoRefreshEnabled(e.target.checked)}
                size="small"
              />
            }
            label="Auto-refresh"
          />
          
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Export Data">
            <IconButton onClick={handleExportData}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Settings">
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
          
          {!fullscreen && (
            <Tooltip title="Fullscreen">
              <IconButton>
                <FullscreenIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Critical Alerts Banner */}
      {alerts.filter(alert => alert.severity === 'critical').length > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <AlertTitle>Critical Alerts Detected</AlertTitle>
          {alerts.filter(alert => alert.severity === 'critical').length} critical issue(s) require immediate attention.
        </Alert>
      )}

      {/* Main Dashboard Grid */}
      <Grid container spacing={3}>
        {/* Metrics Overview */}
        <Grid item xs={12}>
          <MetricsOverview metrics={metrics} />
        </Grid>

        {/* System Health */}
        <Grid item xs={12} md={6}>
          <SystemHealth 
            metrics={metrics}
            alerts={alerts}
            status={systemStatus}
          />
        </Grid>

        {/* Alerts Panel */}
        <Grid item xs={12} md={6}>
          <AlertsPanel 
            alerts={alerts}
            onAcknowledge={(alertId) => {
              setAlerts(prev => 
                prev.map(alert => 
                  alert.id === alertId 
                    ? { ...alert, acknowledged: true }
                    : alert
                )
              );
            }}
          />
        </Grid>

        {/* Workflow Monitor */}
        <Grid item xs={12} lg={8}>
          <WorkflowMonitor 
            timeRange={selectedTimeRange}
            onTimeRangeChange={setSelectedTimeRange}
          />
        </Grid>

        {/* Resource Utilization */}
        <Grid item xs={12} lg={4}>
          <ResourceUtilization 
            metrics={metrics?.resources}
            realtime={true}
          />
        </Grid>

        {/* Model Performance */}
        <Grid item xs={12} lg={6}>
          <ModelPerformance 
            metrics={metrics?.models}
            timeRange={selectedTimeRange}
          />
        </Grid>

        {/* Real-time Charts */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Real-time Metrics
              </Typography>
              <RealtimeChart
                metrics={[
                  { name: 'API Requests/min', value: metrics?.models.total_requests_today || 0, color: '#1976d2' },
                  { name: 'GPU Utilization %', value: metrics?.resources.gpu_utilization || 0, color: '#2e7d32' },
                  { name: 'Error Rate %', value: (metrics?.models.error_rate || 0) * 100, color: '#d32f2f' },
                ]}
                height={300}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Revenue Analytics */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Revenue Analytics
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      ${metrics?.revenue.daily_revenue.toLocaleString() || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Daily Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      ${metrics?.revenue.monthly_recurring.toLocaleString() || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Monthly Recurring Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main">
                      {metrics?.revenue.api_calls_today.toLocaleString() || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      API Calls Today
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main">
                      {metrics?.revenue.active_subscriptions || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Subscriptions
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

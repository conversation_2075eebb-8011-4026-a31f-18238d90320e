# Enterprise AI/ML Platform - Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_NAME="Enterprise AI/ML Platform"
VERSION="2.0.0"
DEBUG=false
HOST=0.0.0.0
PORT=8000
WORKERS=4

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256
ENCRYPTION_KEY=your-encryption-key-32-bytes-base64-encoded

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL (Primary Database)
POSTGRES_SERVER=localhost
POSTGRES_USER=aiml_user
POSTGRES_PASSWORD=secure_password_change_me
POSTGRES_DB=aiml_platform
POSTGRES_PORT=5432
DATABASE_URL=postgresql+asyncpg://aiml_user:secure_password_change_me@localhost:5432/aiml_platform

# MongoDB (Document Storage)
MONGODB_URL=*********************************************************
MONGODB_DB_NAME=aiml_platform

# Redis (Caching & Sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=secure_password_change_me
REDIS_DB=0
REDIS_URL=redis://:secure_password_change_me@localhost:6379/0

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================
CELERY_BROKER_URL=redis://:secure_password_change_me@localhost:6379/1
CELERY_RESULT_BACKEND=redis://:secure_password_change_me@localhost:6379/2

# =============================================================================
# CORS & SECURITY
# =============================================================================
ALLOWED_HOSTS=http://localhost:3000,http://localhost:8080,https://yourdomain.com

# =============================================================================
# ML & AI CONFIGURATION
# =============================================================================

# Model Storage
ML_MODEL_STORAGE_PATH=/app/models
ML_DATA_STORAGE_PATH=/app/data
ML_TEMP_STORAGE_PATH=/tmp/ml

# GPU Configuration
CUDA_VISIBLE_DEVICES=0,1,2,3
GPU_MEMORY_FRACTION=0.8

# External AI APIs
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
HUGGINGFACE_API_KEY=your-huggingface-api-key-here

# =============================================================================
# CLOUD STORAGE (AWS S3)
# =============================================================================
FILE_STORAGE_TYPE=local  # local, s3, gcs, azure
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_S3_BUCKET=your-s3-bucket-name
AWS_REGION=us-east-1

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_AUDIT_LOG=true

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME="AI/ML Platform"

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_COMPUTER_VISION=true
ENABLE_NLP=true
ENABLE_TIME_SERIES=true
ENABLE_REINFORCEMENT_LEARNING=true
ENABLE_GENERATIVE_AI=true
ENABLE_QUANTUM_ML=false
ENABLE_FEDERATED_LEARNING=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Jupyter Configuration
JUPYTER_TOKEN=secure_token_change_me

# MinIO (Local S3-compatible storage)
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin123

# Grafana
GRAFANA_PASSWORD=admin

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# SSL/TLS
SSL_ENABLED=false
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# Load Balancer
LOAD_BALANCER_ENABLED=false

# Auto-scaling
AUTO_SCALING_ENABLED=false
MIN_REPLICAS=2
MAX_REPLICAS=10

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# =============================================================================
# KUBERNETES CONFIGURATION
# =============================================================================
KUBERNETES_NAMESPACE=aiml-platform
KUBERNETES_SERVICE_ACCOUNT=aiml-platform-backend

# =============================================================================
# TERRAFORM CONFIGURATION
# =============================================================================
TF_VAR_environment=development
TF_VAR_aws_region=us-east-1
TF_VAR_project_name=aiml-platform

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
DOCKER_REGISTRY=ghcr.io
DOCKER_IMAGE_TAG=latest

# =============================================================================
# CI/CD CONFIGURATION
# =============================================================================
GITHUB_TOKEN=your-github-token
SONAR_TOKEN=your-sonar-token
CODECOV_TOKEN=your-codecov-token

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================

# Slack Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# DataDog (if using)
DATADOG_API_KEY=your-datadog-api-key
DATADOG_APP_KEY=your-datadog-app-key

# New Relic (if using)
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key

# =============================================================================
# ADVANCED ML CONFIGURATION
# =============================================================================

# AutoML Settings
AUTOML_TIME_LIMIT=3600  # seconds
AUTOML_MEMORY_LIMIT=8192  # MB

# Model Serving
MODEL_SERVING_ENABLED=true
MODEL_SERVING_PORT=8001
MODEL_SERVING_WORKERS=2

# Experiment Tracking
MLFLOW_TRACKING_URI=http://localhost:5000
WANDB_PROJECT=aiml-platform
WANDB_ENTITY=your-wandb-entity

# Data Versioning
DVC_REMOTE_URL=s3://your-dvc-bucket/data

# =============================================================================
# SECURITY COMPLIANCE
# =============================================================================

# GDPR Compliance
GDPR_ENABLED=false
DATA_RETENTION_DAYS=365

# HIPAA Compliance
HIPAA_ENABLED=false
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years

# SOC2 Compliance
SOC2_ENABLED=false
ENCRYPTION_AT_REST=true
ENCRYPTION_IN_TRANSIT=true

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Database Connection Pooling
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=3600

# Redis Connection Pooling
REDIS_MAX_CONNECTIONS=20

# HTTP Client Settings
HTTP_TIMEOUT=30
HTTP_MAX_CONNECTIONS=100

# Async Settings
ASYNC_WORKERS=4
ASYNC_QUEUE_SIZE=1000

# =============================================================================
# CUSTOM SETTINGS
# =============================================================================

# Add your custom environment variables here
# CUSTOM_SETTING_1=value1
# CUSTOM_SETTING_2=value2

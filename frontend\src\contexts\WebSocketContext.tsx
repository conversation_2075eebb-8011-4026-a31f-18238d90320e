/**
 * WebSocket Context for the Enterprise AI/ML Platform.
 * 
 * Provides real-time communication capabilities throughout the application,
 * including connection management, message handling, and event subscriptions.
 */

import React, { createContext, useContext, useEffect, useRef, useState, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { toast } from 'react-toastify';

// Types
interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface WebSocketContextType {
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  sendMessage: (message: WebSocketMessage) => void;
  subscribe: (topics: string[]) => void;
  unsubscribe: (topics: string[]) => void;
  lastMessage: WebSocketMessage | null;
  connectionError: string | null;
}

// Create context
const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

// Custom hook to use WebSocket context
export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

// WebSocket provider component
interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 3000; // 3 seconds

  // Generate client ID
  const clientId = useRef(`client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  // Connect to WebSocket
  const connect = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionStatus('connecting');
    setConnectionError(null);

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/${clientId.current}`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setConnectionStatus('connected');
        setConnectionError(null);
        reconnectAttempts.current = 0;

        // Send authentication if user is logged in
        if (isAuthenticated && user) {
          sendMessage({
            type: 'authenticate',
            user_id: user.id,
            token: localStorage.getItem('token'),
          });
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          setLastMessage(message);
          handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setConnectionStatus('disconnected');
        wsRef.current = null;

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          scheduleReconnect();
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
        setConnectionError('WebSocket connection error');
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setConnectionStatus('error');
      setConnectionError('Failed to create WebSocket connection');
    }
  };

  // Schedule reconnection
  const scheduleReconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    reconnectAttempts.current += 1;
    const delay = reconnectDelay * Math.pow(2, reconnectAttempts.current - 1); // Exponential backoff

    console.log(`Scheduling WebSocket reconnection attempt ${reconnectAttempts.current} in ${delay}ms`);

    reconnectTimeoutRef.current = setTimeout(() => {
      if (reconnectAttempts.current <= maxReconnectAttempts) {
        connect();
      } else {
        console.error('Max WebSocket reconnection attempts reached');
        setConnectionError('Unable to establish WebSocket connection');
      }
    }, delay);
  };

  // Disconnect WebSocket
  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Normal closure');
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionStatus('disconnected');
    reconnectAttempts.current = 0;
  };

  // Send message
  const sendMessage = (message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
      }
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  };

  // Subscribe to topics
  const subscribe = (topics: string[]) => {
    sendMessage({
      type: 'subscribe',
      topics,
    });
  };

  // Unsubscribe from topics
  const unsubscribe = (topics: string[]) => {
    sendMessage({
      type: 'unsubscribe',
      topics,
    });
  };

  // Handle incoming messages
  const handleMessage = (message: WebSocketMessage) => {
    switch (message.type) {
      case 'welcome':
        console.log('WebSocket welcome message:', message.message);
        break;

      case 'notification':
        toast.info(message.notification.message || 'New notification');
        break;

      case 'progress_update':
        // Handle progress updates for long-running tasks
        console.log('Progress update:', message);
        break;

      case 'ml_event':
        // Handle ML-related events
        console.log('ML event:', message);
        if (message.event_type === 'model_training_complete') {
          toast.success(`Model training completed: ${message.data.model_name}`);
        } else if (message.event_type === 'model_training_failed') {
          toast.error(`Model training failed: ${message.data.error}`);
        }
        break;

      case 'system_event':
        // Handle system events
        console.log('System event:', message);
        break;

      case 'error':
        console.error('WebSocket error message:', message.message);
        toast.error(message.message);
        break;

      case 'pong':
        // Handle ping/pong for connection health
        break;

      default:
        console.log('Unhandled WebSocket message:', message);
    }
  };

  // Connect when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [isAuthenticated]);

  // Ping/pong for connection health
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      sendMessage({ type: 'ping' });
    }, 30000); // Ping every 30 seconds

    return () => clearInterval(pingInterval);
  }, [isConnected]);

  // Context value
  const contextValue: WebSocketContextType = {
    isConnected,
    connectionStatus,
    sendMessage,
    subscribe,
    unsubscribe,
    lastMessage,
    connectionError,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

"""
Agent Database Models
=====================

SQLAlchemy models for agent management and task tracking.
"""

from sqlalchemy import Column, String, Text, DateTime, Float, Integer, <PERSON>olean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()


class Agent(Base):
    """Agent model for storing agent metadata and state."""
    
    __tablename__ = "agents"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String(255), unique=True, nullable=False)  # Unique agent identifier
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Agent type and capabilities
    agent_type = Column(String(100), nullable=False)  # architect, engineer, trainer, etc.
    capabilities = Column(JSON)  # List of capabilities
    
    # Configuration
    config = Column(JSON)  # Agent configuration
    version = Column(String(50), default="1.0.0")
    
    # Status
    status = Column(String(50), default="inactive")  # inactive, active, busy, error
    is_enabled = Column(<PERSON>ole<PERSON>, default=True)
    
    # Performance metrics
    total_tasks_completed = Column(Integer, default=0)
    total_tasks_failed = Column(Integer, default=0)
    avg_task_duration_seconds = Column(Float, default=0.0)
    success_rate = Column(Float, default=0.0)
    
    # Resource usage
    cpu_usage_avg = Column(Float, default=0.0)
    memory_usage_avg = Column(Float, default=0.0)
    
    # Learning and improvement
    learning_rate = Column(Float, default=0.01)
    improvement_score = Column(Float, default=0.0)
    last_improvement = Column(DateTime)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_activity = Column(DateTime)
    
    # Relationships
    tasks = relationship("AgentTask", back_populates="agent")
    
    def __repr__(self):
        return f"<Agent(id='{self.agent_id}', name='{self.name}')>"
    
    def calculate_success_rate(self):
        """Calculate agent success rate."""
        total_tasks = self.total_tasks_completed + self.total_tasks_failed
        if total_tasks > 0:
            self.success_rate = self.total_tasks_completed / total_tasks
        return self.success_rate
    
    def update_performance_metrics(self):
        """Update performance metrics based on completed tasks."""
        completed_tasks = [task for task in self.tasks if task.status == "completed"]
        
        if completed_tasks:
            # Calculate average duration
            durations = [task.duration_seconds for task in completed_tasks if task.duration_seconds]
            if durations:
                self.avg_task_duration_seconds = sum(durations) / len(durations)
            
            # Update success rate
            self.calculate_success_rate()


class AgentTask(Base):
    """Agent task model for tracking individual tasks."""
    
    __tablename__ = "agent_tasks"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    task_id = Column(String(255), unique=True, nullable=False)
    
    # Task details
    name = Column(String(255), nullable=False)
    description = Column(Text)
    task_type = Column(String(100))
    
    # Assignment
    agent_id = Column(String, ForeignKey("agents.id"), nullable=False)
    assigned_by = Column(String)  # User ID or system
    
    # Task data
    parameters = Column(JSON)  # Task parameters
    inputs = Column(JSON)  # Task inputs
    outputs = Column(JSON)  # Task outputs
    
    # Execution details
    status = Column(String(50), default="pending")  # pending, running, completed, failed, cancelled
    progress = Column(Float, default=0.0)  # 0.0 to 1.0
    priority = Column(Integer, default=1)  # 1-10, higher is more important
    
    # Timing
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    duration_seconds = Column(Float)
    
    # Results
    success = Column(Boolean)
    error_message = Column(Text)
    error_details = Column(JSON)
    
    # Resource usage
    cpu_usage = Column(Float)
    memory_usage = Column(Float)
    
    # Dependencies
    depends_on = Column(JSON)  # List of task IDs this task depends on
    
    # Relationships
    agent = relationship("Agent", back_populates="tasks")
    
    def __repr__(self):
        return f"<AgentTask(id='{self.task_id}', name='{self.name}')>"
    
    @property
    def is_running(self):
        """Check if task is currently running."""
        return self.status in ["pending", "running"]
    
    @property
    def is_completed(self):
        """Check if task is completed (success or failure)."""
        return self.status in ["completed", "failed", "cancelled"]
    
    def calculate_duration(self):
        """Calculate task duration."""
        if self.started_at and self.completed_at:
            delta = self.completed_at - self.started_at
            self.duration_seconds = delta.total_seconds()
        return self.duration_seconds
    
    def mark_completed(self, success: bool = True, outputs: dict = None, error_message: str = None):
        """Mark task as completed."""
        self.completed_at = datetime.utcnow()
        self.status = "completed" if success else "failed"
        self.success = success
        
        if outputs:
            self.outputs = outputs
        
        if error_message:
            self.error_message = error_message
        
        self.calculate_duration()
    
    def mark_started(self):
        """Mark task as started."""
        self.started_at = datetime.utcnow()
        self.status = "running"

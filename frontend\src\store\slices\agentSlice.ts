/**
 * Agent slice for Redux store.
 * 
 * Manages AI agent state including agent creation, configuration,
 * and execution.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface Agent {
  id: string;
  name: string;
  type: 'data_processor' | 'model_trainer' | 'predictor' | 'custom';
  status: 'active' | 'inactive' | 'running' | 'error';
  config: Record<string, any>;
  created_at: string;
  updated_at: string;
  last_run?: string;
  performance_metrics?: {
    success_rate: number;
    avg_execution_time: number;
    total_runs: number;
  };
}

export interface AgentState {
  agents: Agent[];
  currentAgent: Agent | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: AgentState = {
  agents: [],
  currentAgent: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchAgents = createAsyncThunk(
  'agent/fetchAgents',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/v1/agents');
      if (!response.ok) throw new Error('Failed to fetch agents');
      return await response.json();
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error');
    }
  }
);

const agentSlice = createSlice({
  name: 'agent',
  initialState,
  reducers: {
    setCurrentAgent: (state, action: PayloadAction<Agent | null>) => {
      state.currentAgent = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAgents.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAgents.fulfilled, (state, action) => {
        state.isLoading = false;
        state.agents = action.payload;
      })
      .addCase(fetchAgents.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setCurrentAgent, clearError } = agentSlice.actions;
export const selectAgents = (state: { agent: AgentState }) => state.agent.agents;
export const selectCurrentAgent = (state: { agent: AgentState }) => state.agent.currentAgent;
export const selectAgentLoading = (state: { agent: AgentState }) => state.agent.isLoading;
export const selectAgentError = (state: { agent: AgentState }) => state.agent.error;

export default agentSlice.reducer;

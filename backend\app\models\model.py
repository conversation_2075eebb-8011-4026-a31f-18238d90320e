"""
ML Model Database Models
========================

SQLAlchemy models for ML model management.
"""

from sqlalchemy import Column, String, Text, DateTime, Float, Integer, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()


class MLModel(Base):
    """ML Model model for storing model metadata."""
    
    __tablename__ = "ml_models"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Model details
    framework = Column(String(100))  # pytorch, tensorflow, sklearn, etc.
    model_type = Column(String(100))  # classification, regression, generation, etc.
    version = Column(String(50), default="1.0.0")
    
    # Model files
    model_path = Column(String(500))  # Path to model file
    config_path = Column(String(500))  # Path to config file
    weights_path = Column(String(500))  # Path to weights file
    
    # Schema
    input_schema = Column(JSON)  # Input data schema
    output_schema = Column(JSON)  # Output data schema
    
    # Performance metrics
    accuracy = Column(Float)
    precision = Column(Float)
    recall = Column(Float)
    f1_score = Column(Float)
    
    # Training details
    training_dataset = Column(String(255))
    training_duration_hours = Column(Float)
    training_cost_usd = Column(Float)
    
    # Resource requirements
    min_cpu_cores = Column(Integer, default=1)
    min_memory_gb = Column(Float, default=1.0)
    requires_gpu = Column(Boolean, default=False)
    min_gpu_memory_gb = Column(Float)
    
    # Ownership
    owner_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Status
    status = Column(String(50), default="training")  # training, ready, deployed, deprecated
    is_public = Column(Boolean, default=False)
    
    # Marketplace
    is_marketplace_item = Column(Boolean, default=False)
    marketplace_price = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    trained_at = Column(DateTime)
    
    # Usage statistics
    total_predictions = Column(Integer, default=0)
    avg_latency_ms = Column(Float, default=0.0)
    error_rate = Column(Float, default=0.0)
    
    # Relationships
    owner = relationship("User", back_populates="models")
    deployments = relationship("ModelDeployment", back_populates="model")
    
    def __repr__(self):
        return f"<MLModel(id='{self.id}', name='{self.name}')>"


class ModelDeployment(Base):
    """Model deployment model for tracking deployed models."""
    
    __tablename__ = "model_deployments"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    model_id = Column(String, ForeignKey("ml_models.id"), nullable=False)
    
    # Deployment details
    name = Column(String(255), nullable=False)
    endpoint_url = Column(String(500))
    
    # Configuration
    deployment_config = Column(JSON)
    environment = Column(String(50), default="production")  # development, staging, production
    
    # Scaling
    min_replicas = Column(Integer, default=1)
    max_replicas = Column(Integer, default=10)
    current_replicas = Column(Integer, default=1)
    auto_scaling = Column(Boolean, default=True)
    
    # Resource allocation
    cpu_request = Column(Float, default=1.0)
    memory_request_gb = Column(Float, default=2.0)
    gpu_request = Column(Float, default=0.0)
    
    # Status
    status = Column(String(50), default="deploying")  # deploying, running, stopped, failed
    health_status = Column(String(50), default="unknown")  # healthy, unhealthy, unknown
    
    # Performance metrics
    requests_per_second = Column(Float, default=0.0)
    avg_response_time_ms = Column(Float, default=0.0)
    error_rate = Column(Float, default=0.0)
    uptime_percentage = Column(Float, default=0.0)
    
    # Cost tracking
    hourly_cost_usd = Column(Float, default=0.0)
    total_cost_usd = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deployed_at = Column(DateTime)
    stopped_at = Column(DateTime)
    
    # Relationships
    model = relationship("MLModel", back_populates="deployments")
    
    def __repr__(self):
        return f"<ModelDeployment(id='{self.id}', name='{self.name}')>"
    
    @property
    def is_running(self):
        """Check if deployment is currently running."""
        return self.status == "running"
    
    @property
    def is_healthy(self):
        """Check if deployment is healthy."""
        return self.health_status == "healthy"
    
    def calculate_uptime(self):
        """Calculate deployment uptime percentage."""
        if self.deployed_at:
            total_time = (datetime.utcnow() - self.deployed_at).total_seconds()
            if total_time > 0:
                # This would be calculated from actual monitoring data
                # For now, return a placeholder
                return 99.5
        return 0.0

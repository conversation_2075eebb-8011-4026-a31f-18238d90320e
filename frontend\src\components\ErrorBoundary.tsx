/**
 * Error Boundary component for the Enterprise AI/ML Platform.
 * 
 * Catches JavaScript errors anywhere in the component tree and displays
 * a fallback UI instead of crashing the entire application.
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Container,
  Alert,
  <PERSON>ertTitle,
  Divider,
} from '@mui/material';
import { ErrorOutline, Refresh, BugReport } from '@mui/icons-material';

// Types
interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

// Error Boundary Class Component
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Report error to monitoring service
    this.reportError(error, errorInfo);
  }

  reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // In a real application, you would send this to your error reporting service
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      // Example: Send to error reporting service
      // fetch('/api/v1/errors/report', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport),
      // });

      console.log('Error report:', errorReport);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Container maxWidth="md" sx={{ mt: 4 }}>
          <Paper elevation={3} sx={{ p: 4 }}>
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              textAlign="center"
              gap={3}
            >
              <ErrorOutline color="error" sx={{ fontSize: 80 }} />
              
              <Typography variant="h4" color="error" gutterBottom>
                Oops! Something went wrong
              </Typography>
              
              <Typography variant="body1" color="text.secondary" paragraph>
                We're sorry, but an unexpected error occurred. Our team has been notified
                and is working to fix the issue.
              </Typography>

              <Alert severity="error" sx={{ width: '100%', textAlign: 'left' }}>
                <AlertTitle>Error Details</AlertTitle>
                <Typography variant="body2" component="div">
                  <strong>Error ID:</strong> {this.state.errorId}
                </Typography>
                <Typography variant="body2" component="div">
                  <strong>Message:</strong> {this.state.error?.message}
                </Typography>
              </Alert>

              <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<Refresh />}
                  onClick={this.handleReload}
                >
                  Reload Page
                </Button>
                
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={this.handleReset}
                >
                  Try Again
                </Button>
                
                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<BugReport />}
                  onClick={() => {
                    const subject = encodeURIComponent(`Error Report - ${this.state.errorId}`);
                    const body = encodeURIComponent(
                      `Error ID: ${this.state.errorId}\n` +
                      `Message: ${this.state.error?.message}\n` +
                      `URL: ${window.location.href}\n` +
                      `Timestamp: ${new Date().toISOString()}\n\n` +
                      `Please describe what you were doing when this error occurred:\n\n`
                    );
                    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
                  }}
                >
                  Report Issue
                </Button>
              </Box>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <>
                  <Divider sx={{ width: '100%', mt: 3 }} />
                  
                  <Box sx={{ width: '100%', textAlign: 'left' }}>
                    <Typography variant="h6" gutterBottom>
                      Development Error Details
                    </Typography>
                    
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 2,
                        backgroundColor: '#f5f5f5',
                        fontFamily: 'monospace',
                        fontSize: '0.875rem',
                        overflow: 'auto',
                        maxHeight: 300,
                      }}
                    >
                      <Typography variant="body2" component="pre">
                        {this.state.error.stack}
                      </Typography>
                      
                      {this.state.errorInfo && (
                        <>
                          <Divider sx={{ my: 2 }} />
                          <Typography variant="body2" component="pre">
                            {this.state.errorInfo.componentStack}
                          </Typography>
                        </>
                      )}
                    </Paper>
                  </Box>
                </>
              )}
            </Box>
          </Paper>
        </Container>
      );
    }

    return this.props.children;
  }
}

// Functional component wrapper for easier usage
interface ErrorBoundaryWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export const ErrorBoundaryWrapper: React.FC<ErrorBoundaryWrapperProps> = ({
  children,
  fallback,
}) => {
  return (
    <ErrorBoundary fallback={fallback}>
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundary;

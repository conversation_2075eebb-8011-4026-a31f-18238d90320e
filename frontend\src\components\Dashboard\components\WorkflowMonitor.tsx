/**
 * Workflow Monitor Component for NeuroFlowAI Dashboard
 * ===================================================
 * 
 * Real-time monitoring of workflow executions with detailed status tracking.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Avatar,
  AvatarGroup,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';

interface WorkflowExecution {
  id: string;
  name: string;
  status: 'running' | 'completed' | 'failed' | 'paused' | 'cancelled';
  progress: number;
  current_stage: string;
  start_time: string;
  end_time?: string;
  duration?: number;
  user: {
    id: string;
    name: string;
    avatar?: string;
  };
  agents_involved: string[];
  resource_usage: {
    cpu: number;
    memory: number;
    gpu?: number;
  };
  cost_accumulated: number;
}

interface WorkflowMonitorProps {
  timeRange: string;
  onTimeRangeChange: (range: string) => void;
}

export const WorkflowMonitor: React.FC<WorkflowMonitorProps> = ({
  timeRange,
  onTimeRangeChange,
}) => {
  const [workflows, setWorkflows] = useState<WorkflowExecution[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);

  // Fetch workflow data
  useEffect(() => {
    const fetchWorkflows = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/v1/workflows?timeRange=${timeRange}&status=all`);
        const data = await response.json();
        setWorkflows(data.workflows || []);
      } catch (error) {
        console.error('Failed to fetch workflows:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflows();
    
    // Set up real-time updates
    const interval = setInterval(fetchWorkflows, 5000);
    return () => clearInterval(interval);
  }, [timeRange]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'primary';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'paused':
        return 'warning';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <PlayIcon sx={{ fontSize: 16 }} />;
      case 'paused':
        return <PauseIcon sx={{ fontSize: 16 }} />;
      case 'cancelled':
        return <StopIcon sx={{ fontSize: 16 }} />;
      default:
        return null;
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    }
    if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const calculateDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    return (end.getTime() - start.getTime()) / 1000;
  };

  const handleWorkflowAction = async (workflowId: string, action: 'pause' | 'resume' | 'cancel') => {
    try {
      await fetch(`/api/v1/workflows/${workflowId}/${action}`, {
        method: 'POST',
      });
      // Refresh workflows after action
      const response = await fetch(`/api/v1/workflows?timeRange=${timeRange}&status=all`);
      const data = await response.json();
      setWorkflows(data.workflows || []);
    } catch (error) {
      console.error(`Failed to ${action} workflow:`, error);
    }
  };

  const getAgentAvatars = (agents: string[]) => {
    const agentColors = {
      'architect': '#1976d2',
      'engineer': '#2e7d32',
      'trainer': '#ed6c02',
      'devops': '#9c27b0',
      'sentinel': '#d32f2f',
      'productizer': '#0288d1',
    };

    return agents.map((agent, index) => (
      <Avatar
        key={agent}
        sx={{
          width: 24,
          height: 24,
          fontSize: 10,
          bgcolor: agentColors[agent.toLowerCase()] || '#757575',
        }}
      >
        {agent.charAt(0).toUpperCase()}
      </Avatar>
    ));
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Workflow Executions
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={(e) => onTimeRangeChange(e.target.value)}
              >
                <MenuItem value="1h">Last Hour</MenuItem>
                <MenuItem value="6h">Last 6 Hours</MenuItem>
                <MenuItem value="24h">Last 24 Hours</MenuItem>
                <MenuItem value="7d">Last 7 Days</MenuItem>
                <MenuItem value="30d">Last 30 Days</MenuItem>
              </Select>
            </FormControl>
            
            <Tooltip title="Refresh">
              <IconButton size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {loading ? (
          <LinearProgress />
        ) : (
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Workflow</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Progress</TableCell>
                  <TableCell>Stage</TableCell>
                  <TableCell>Duration</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Agents</TableCell>
                  <TableCell>Cost</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {workflows.map((workflow) => (
                  <TableRow
                    key={workflow.id}
                    hover
                    selected={selectedWorkflow === workflow.id}
                    onClick={() => setSelectedWorkflow(workflow.id)}
                    sx={{ cursor: 'pointer' }}
                  >
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {workflow.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {workflow.id}
                        </Typography>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(workflow.status)}
                        label={workflow.status.toUpperCase()}
                        color={getStatusColor(workflow.status) as any}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    
                    <TableCell>
                      <Box sx={{ width: 100 }}>
                        <LinearProgress
                          variant="determinate"
                          value={workflow.progress * 100}
                          sx={{ mb: 0.5 }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {(workflow.progress * 100).toFixed(0)}%
                        </Typography>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {workflow.current_stage.replace('_', ' ')}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {formatDuration(
                          workflow.duration || 
                          calculateDuration(workflow.start_time, workflow.end_time)
                        )}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar
                          src={workflow.user.avatar}
                          sx={{ width: 24, height: 24 }}
                        >
                          {workflow.user.name.charAt(0)}
                        </Avatar>
                        <Typography variant="body2">
                          {workflow.user.name}
                        </Typography>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 24, height: 24 } }}>
                        {getAgentAvatars(workflow.agents_involved)}
                      </AvatarGroup>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2" color="success.main">
                        ${workflow.cost_accumulated.toFixed(2)}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <ViewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        
                        <Tooltip title="Timeline">
                          <IconButton size="small">
                            <TimelineIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        
                        {workflow.status === 'running' && (
                          <Tooltip title="Pause">
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleWorkflowAction(workflow.id, 'pause');
                              }}
                            >
                              <PauseIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        
                        {workflow.status === 'paused' && (
                          <Tooltip title="Resume">
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleWorkflowAction(workflow.id, 'resume');
                              }}
                            >
                              <PlayIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        
                        {(workflow.status === 'running' || workflow.status === 'paused') && (
                          <Tooltip title="Cancel">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleWorkflowAction(workflow.id, 'cancel');
                              }}
                            >
                              <StopIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        
                        {workflow.status === 'completed' && (
                          <Tooltip title="Download Results">
                            <IconButton size="small">
                              <DownloadIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {workflows.length === 0 && !loading && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              No workflows found for the selected time range.
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

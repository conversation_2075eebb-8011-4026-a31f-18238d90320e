#!/bin/bash

# Enterprise AI/ML Platform Development Script
# This script provides convenient commands for development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Enterprise AI/ML Platform Development Script"
    echo
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  start           Start all services (backend, frontend, databases)"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  backend         Start only backend"
    echo "  frontend        Start only frontend"
    echo "  worker          Start Celery worker"
    echo "  scheduler       Start Celery beat scheduler"
    echo "  test            Run all tests"
    echo "  test-backend    Run backend tests"
    echo "  test-frontend   Run frontend tests"
    echo "  lint            Run linting for all components"
    echo "  format          Format code for all components"
    echo "  clean           Clean up build artifacts and caches"
    echo "  logs            Show logs from all services"
    echo "  status          Show status of all services"
    echo "  db-reset        Reset database (WARNING: deletes all data)"
    echo "  db-migrate      Run database migrations"
    echo "  help            Show this help message"
    echo
}

# Function to start all services
start_all() {
    print_status "Starting all services..."
    
    # Start databases
    print_status "Starting databases..."
    docker-compose up -d postgres redis
    
    # Wait for databases to be ready
    sleep 5
    
    # Start backend in background
    print_status "Starting backend..."
    cd backend
    source .venv/bin/activate
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    cd ..
    
    # Start frontend in background
    print_status "Starting frontend..."
    cd frontend
    bun dev &
    FRONTEND_PID=$!
    cd ..
    
    print_success "All services started!"
    print_status "Backend: http://localhost:8000"
    print_status "Frontend: http://localhost:3000"
    print_status "API Docs: http://localhost:8000/docs"
    
    # Wait for user input to stop
    echo
    echo "Press Ctrl+C to stop all services..."
    trap 'kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; docker-compose stop postgres redis; exit' INT
    wait
}

# Function to stop all services
stop_all() {
    print_status "Stopping all services..."
    
    # Stop Docker services
    docker-compose stop
    
    # Kill any running processes
    pkill -f "uvicorn app.main:app" || true
    pkill -f "bun dev" || true
    pkill -f "celery" || true
    
    print_success "All services stopped!"
}

# Function to start backend only
start_backend() {
    print_status "Starting backend..."
    
    # Start databases
    docker-compose up -d postgres redis
    
    # Start backend
    cd backend
    source .venv/bin/activate
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
}

# Function to start frontend only
start_frontend() {
    print_status "Starting frontend..."
    
    cd frontend
    bun dev
}

# Function to start Celery worker
start_worker() {
    print_status "Starting Celery worker..."
    
    # Start Redis
    docker-compose up -d redis
    
    cd backend
    source .venv/bin/activate
    celery -A app.tasks.ml_tasks worker --loglevel=info
}

# Function to start Celery scheduler
start_scheduler() {
    print_status "Starting Celery beat scheduler..."
    
    # Start Redis
    docker-compose up -d redis
    
    cd backend
    source .venv/bin/activate
    celery -A app.tasks.ml_tasks beat --loglevel=info
}

# Function to run all tests
run_all_tests() {
    print_status "Running all tests..."
    
    # Backend tests
    print_status "Running backend tests..."
    cd backend
    source .venv/bin/activate
    pytest
    cd ..
    
    # Frontend tests
    print_status "Running frontend tests..."
    cd frontend
    bun test
    cd ..
    
    print_success "All tests completed!"
}

# Function to run backend tests
run_backend_tests() {
    print_status "Running backend tests..."
    
    cd backend
    source .venv/bin/activate
    pytest
}

# Function to run frontend tests
run_frontend_tests() {
    print_status "Running frontend tests..."
    
    cd frontend
    bun test
}

# Function to run linting
run_lint() {
    print_status "Running linting..."
    
    # Backend linting
    print_status "Linting backend..."
    cd backend
    source .venv/bin/activate
    flake8 .
    mypy .
    cd ..
    
    # Frontend linting
    print_status "Linting frontend..."
    cd frontend
    bun run lint
    cd ..
    
    print_success "Linting completed!"
}

# Function to format code
format_code() {
    print_status "Formatting code..."
    
    # Backend formatting
    print_status "Formatting backend..."
    cd backend
    source .venv/bin/activate
    black .
    isort .
    cd ..
    
    # Frontend formatting
    print_status "Formatting frontend..."
    cd frontend
    bun run format
    cd ..
    
    print_success "Code formatting completed!"
}

# Function to clean up
clean_up() {
    print_status "Cleaning up..."
    
    # Backend cleanup
    cd backend
    rm -rf __pycache__ .pytest_cache .mypy_cache htmlcov .coverage
    find . -name "*.pyc" -delete
    find . -name "*.pyo" -delete
    cd ..
    
    # Frontend cleanup
    cd frontend
    rm -rf node_modules/.cache build dist coverage
    cd ..
    
    # Docker cleanup
    docker-compose down --volumes --remove-orphans
    docker system prune -f
    
    print_success "Cleanup completed!"
}

# Function to show logs
show_logs() {
    print_status "Showing logs..."
    docker-compose logs -f
}

# Function to show status
show_status() {
    print_status "Service status:"
    docker-compose ps
    
    echo
    print_status "Process status:"
    pgrep -f "uvicorn app.main:app" && echo "Backend: Running" || echo "Backend: Stopped"
    pgrep -f "bun dev" && echo "Frontend: Running" || echo "Frontend: Stopped"
    pgrep -f "celery" && echo "Celery: Running" || echo "Celery: Stopped"
}

# Function to reset database
reset_database() {
    print_warning "This will delete ALL data in the database!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Resetting database..."
        
        # Stop and remove database containers
        docker-compose stop postgres
        docker-compose rm -f postgres
        
        # Start fresh database
        docker-compose up -d postgres
        sleep 5
        
        # Run migrations
        cd backend
        source .venv/bin/activate
        alembic upgrade head
        cd ..
        
        print_success "Database reset completed!"
    else
        print_status "Database reset cancelled."
    fi
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Start database
    docker-compose up -d postgres
    sleep 5
    
    cd backend
    source .venv/bin/activate
    alembic upgrade head
    cd ..
    
    print_success "Database migrations completed!"
}

# Main function
main() {
    case "${1:-help}" in
        start)
            start_all
            ;;
        stop)
            stop_all
            ;;
        restart)
            stop_all
            sleep 2
            start_all
            ;;
        backend)
            start_backend
            ;;
        frontend)
            start_frontend
            ;;
        worker)
            start_worker
            ;;
        scheduler)
            start_scheduler
            ;;
        test)
            run_all_tests
            ;;
        test-backend)
            run_backend_tests
            ;;
        test-frontend)
            run_frontend_tests
            ;;
        lint)
            run_lint
            ;;
        format)
            format_code
            ;;
        clean)
            clean_up
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        db-reset)
            reset_database
            ;;
        db-migrate)
            run_migrations
            ;;
        help|*)
            show_usage
            ;;
    esac
}

# Run main function
main "$@"

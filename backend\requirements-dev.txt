# Development dependencies for Enterprise AI/ML Platform Backend
# These are additional packages needed for development, testing, and code quality

# Include production requirements
-r requirements.txt

# Development Tools
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.3.1
httpx==0.25.2
factory-boy==3.3.0
faker==20.1.0

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5
pre-commit==3.5.0

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
sphinx-autodoc-typehints==1.25.2

# Debugging
pdb++==0.10.3
ipdb==0.13.13

# Performance Profiling
py-spy==0.3.14
memory-profiler==0.61.0
line-profiler==4.1.1

# Database Tools
alembic==1.12.1
pgcli==4.0.1

# API Testing
httpie==3.2.2

# Type Checking
types-redis==********
types-requests==*********
types-PyYAML==*********

# Linting Extensions
flake8-docstrings==1.7.0
flake8-import-order==0.18.2
flake8-bugbear==23.11.28

# Security
safety==2.3.5

# Environment Management
python-dotenv==1.0.0

# Development Server
watchdog==3.0.0

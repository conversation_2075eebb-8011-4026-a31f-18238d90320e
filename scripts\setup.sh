#!/bin/bash

# Enterprise AI/ML Platform Setup Script
# This script sets up the development environment for the platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Python version
check_python_version() {
    if command_exists python3; then
        python_version=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        required_version="3.11"
        
        if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
            print_success "Python $python_version is installed"
            return 0
        else
            print_error "Python $required_version or higher is required. Found: $python_version"
            return 1
        fi
    else
        print_error "Python 3 is not installed"
        return 1
    fi
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        node_version=$(node -v | sed 's/v//')
        required_version="18.0.0"
        
        if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
            print_success "Node.js $node_version is installed"
            return 0
        else
            print_error "Node.js $required_version or higher is required. Found: $node_version"
            return 1
        fi
    else
        print_error "Node.js is not installed"
        return 1
    fi
}

# Function to install uv
install_uv() {
    if ! command_exists uv; then
        print_status "Installing uv..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        export PATH="$HOME/.cargo/bin:$PATH"
        
        if command_exists uv; then
            print_success "uv installed successfully"
        else
            print_error "Failed to install uv"
            return 1
        fi
    else
        print_success "uv is already installed"
    fi
}

# Function to install Bun
install_bun() {
    if ! command_exists bun; then
        print_status "Installing Bun..."
        curl -fsSL https://bun.sh/install | bash
        export PATH="$HOME/.bun/bin:$PATH"
        
        if command_exists bun; then
            print_success "Bun installed successfully"
        else
            print_error "Failed to install Bun"
            return 1
        fi
    else
        print_success "Bun is already installed"
    fi
}

# Function to setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Create virtual environment and install dependencies
    print_status "Creating virtual environment with uv..."
    uv venv
    
    print_status "Installing dependencies..."
    source .venv/bin/activate
    uv sync --dev
    
    # Copy environment file if it doesn't exist
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
            print_warning "Please edit .env file with your configuration"
        else
            print_warning ".env.example not found"
        fi
    fi
    
    cd ..
    print_success "Backend setup completed"
}

# Function to setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing dependencies with Bun..."
    bun install
    
    cd ..
    print_success "Frontend setup completed"
}

# Function to setup Docker environment
setup_docker() {
    print_status "Setting up Docker environment..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        return 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        return 1
    fi
    
    # Copy environment file if it doesn't exist
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
            print_warning "Please edit .env file with your configuration"
        fi
    fi
    
    print_success "Docker environment setup completed"
}

# Function to run initial database setup
setup_database() {
    print_status "Setting up database..."
    
    # Start database services
    print_status "Starting database services..."
    docker-compose up -d postgres redis
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Run database migrations
    print_status "Running database migrations..."
    cd backend
    source .venv/bin/activate
    
    # Check if alembic is available
    if command_exists alembic; then
        alembic upgrade head
        print_success "Database migrations completed"
    else
        print_warning "Alembic not found. Skipping migrations."
    fi
    
    cd ..
}

# Main setup function
main() {
    print_status "Starting Enterprise AI/ML Platform setup..."
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! check_python_version; then
        print_error "Python version check failed"
        exit 1
    fi
    
    if ! check_node_version; then
        print_error "Node.js version check failed"
        exit 1
    fi
    
    # Install package managers
    install_uv
    install_bun
    
    # Setup components
    setup_docker
    setup_backend
    setup_frontend
    
    # Ask if user wants to setup database
    read -p "Do you want to setup the database now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_database
    else
        print_warning "Database setup skipped. Run 'docker-compose up -d postgres redis' and 'alembic upgrade head' later."
    fi
    
    print_success "Setup completed successfully!"
    echo
    print_status "Next steps:"
    echo "1. Edit .env file with your configuration"
    echo "2. Start the development environment:"
    echo "   - Backend: cd backend && source .venv/bin/activate && uvicorn app.main:app --reload"
    echo "   - Frontend: cd frontend && bun dev"
    echo "   - Or use Docker: docker-compose -f docker-compose.dev.yml up"
    echo "3. Access the application at http://localhost:3000"
}

# Run main function
main "$@"

/**
 * Enterprise AI/ML Platform - Main App Component
 * 
 * Modern React application with TypeScript, Material-UI, and Redux
 * Features: Authentication, Real-time updates, Progressive Web App
 */

import React, { Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, CircularProgress } from '@mui/material';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { ToastContainer } from 'react-toastify';
import { HelmetProvider } from 'react-helmet-async';

// Store and services
import { store } from './store/store';
import { useAppDispatch, useAppSelector } from './store/hooks';
import { authActions } from './store/slices/authSlice';
import { WebSocketService } from './services/websocketService';

// Components
import Layout from './components/Layout/Layout';
import LoadingSpinner from './components/Common/LoadingSpinner';
import ErrorBoundary from './components/Common/ErrorBoundary';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Pages (Lazy loaded for code splitting)
const HomePage = React.lazy(() => import('./pages/HomePage'));
const LoginPage = React.lazy(() => import('./pages/Auth/LoginPage'));
const RegisterPage = React.lazy(() => import('./pages/Auth/RegisterPage'));
const DashboardPage = React.lazy(() => import('./pages/Dashboard/DashboardPage'));
const DataPage = React.lazy(() => import('./pages/Data/DataPage'));
const ModelsPage = React.lazy(() => import('./pages/Models/ModelsPage'));
const ComputerVisionPage = React.lazy(() => import('./pages/AI/ComputerVisionPage'));
const NLPPage = React.lazy(() => import('./pages/AI/NLPPage'));
const TimeSeriesPage = React.lazy(() => import('./pages/AI/TimeSeriesPage'));
const ReinforcementLearningPage = React.lazy(() => import('./pages/AI/ReinforcementLearningPage'));
const GenerativeAIPage = React.lazy(() => import('./pages/AI/GenerativeAIPage'));
const GraphNeuralNetworksPage = React.lazy(() => import('./pages/AI/GraphNeuralNetworksPage'));
const QuantumMLPage = React.lazy(() => import('./pages/AI/QuantumMLPage'));
const FederatedLearningPage = React.lazy(() => import('./pages/AI/FederatedLearningPage'));
const MLOpsPage = React.lazy(() => import('./pages/MLOps/MLOpsPage'));
const AnalyticsPage = React.lazy(() => import('./pages/Analytics/AnalyticsPage'));
const EdgeAIPage = React.lazy(() => import('./pages/AI/EdgeAIPage'));
const AudioProcessingPage = React.lazy(() => import('./pages/AI/AudioProcessingPage'));
const ProfilePage = React.lazy(() => import('./pages/Profile/ProfilePage'));
const SettingsPage = React.lazy(() => import('./pages/Settings/SettingsPage'));

// Styles
import 'react-toastify/dist/ReactToastify.css';
import './App.css';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Create Material-UI theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5983',
      dark: '#9a0036',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

// App component with authentication logic
const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // Initialize authentication state
    dispatch(authActions.initializeAuth());

    // Initialize WebSocket connection if authenticated
    if (isAuthenticated) {
      WebSocketService.connect();
    }

    return () => {
      WebSocketService.disconnect();
    };
  }, [dispatch, isAuthenticated]);

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <LoadingSpinner size={60} />
      </Box>
    );
  }

  return (
    <Router>
      <Routes>
        {/* Public routes */}
        <Route
          path="/login"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <Suspense fallback={<LoadingSpinner />}>
                <LoginPage />
              </Suspense>
            )
          }
        />
        <Route
          path="/register"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <Suspense fallback={<LoadingSpinner />}>
                <RegisterPage />
              </Suspense>
            )
          }
        />
        <Route
          path="/"
          element={
            <Suspense fallback={<LoadingSpinner />}>
              <HomePage />
            </Suspense>
          }
        />

        {/* Protected routes */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <DashboardPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/data"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <DataPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/models"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <ModelsPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />

        {/* AI/ML Module Routes */}
        <Route
          path="/ai/computer-vision"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <ComputerVisionPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/nlp"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <NLPPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/time-series"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <TimeSeriesPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/reinforcement-learning"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <ReinforcementLearningPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/generative-ai"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <GenerativeAIPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/graph-neural-networks"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <GraphNeuralNetworksPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/quantum-ml"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <QuantumMLPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/federated-learning"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <FederatedLearningPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/edge-ai"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <EdgeAIPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/ai/audio-processing"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <AudioProcessingPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />

        {/* MLOps and Analytics */}
        <Route
          path="/mlops"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <MLOpsPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/analytics"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <AnalyticsPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />

        {/* User routes */}
        <Route
          path="/profile"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <ProfilePage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/settings"
          element={
            <ProtectedRoute>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <SettingsPage />
                </Suspense>
              </Layout>
            </ProtectedRoute>
          }
        />

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
};

// Main App component
const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <HelmetProvider>
        <Provider store={store}>
          <QueryClientProvider client={queryClient}>
            <ThemeProvider theme={theme}>
              <CssBaseline />
              <AppContent />
              <ToastContainer
                position="top-right"
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="light"
              />
              <ReactQueryDevtools initialIsOpen={false} />
            </ThemeProvider>
          </QueryClientProvider>
        </Provider>
      </HelmetProvider>
    </ErrorBoundary>
  );
};

export default App;
